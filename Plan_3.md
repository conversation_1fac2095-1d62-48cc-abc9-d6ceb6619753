## Implementation Plan

### 1. **Voice Input Integration**

*   **File:** `src/components/InputArea/InputArea.tsx`
*   **Action:** Integrate the Web Speech API directly into the component. We will use `window.webkitSpeechRecognition` which is the standard implementation in Chromium-based environments like CEP. We don't need to download any external tools as this API is part of the browser engine itself.
*   **Logic:**
    *   Add state to manage listening status (`isListening`).
    *   On `Mic` button click, create a new `SpeechRecognition` instance, set its properties (e.g., `continuous`, `interimResults`), and start it.
    *   Implement `onresult` to append the transcribed text to the input area.
    *   Implement `onend` and `onerror` for graceful state management.

### 2. **Searchable Model Selection**

*   **File (New):** `src/components/Common/SearchableSelect.tsx`
*   **Action:** Create a new, reusable searchable dropdown component exactly as you've specified in the logic summary.
*   **File (Modified):** `src/components/TopBar/TopBar.tsx`
*   **Action:** Replace the static text display for the provider/model with the new `SearchableSelect` component. This will allow users to search and filter the model list for the currently active provider.

### 3. **Enhanced Error Handling**

*   **File (Modified):** `src/types/index.ts`
    *   **Action:** Add a `status` field to the `Message` interface (`'sent' | 'error' | 'streaming'`) to track the state of each message.
*   **File (Modified):** `src/stores/chatStore.ts`
    *   **Action:**
        *   Update the store to manage the new `status` field for messages.
        *   Implement a `retryLastUserMessage` function. When a message fails to send, this function will be called to try the API request again.
*   **File (Modified):** `src/components/ChatInterface/MessageBubble.tsx`
    *   **Action:** When a message has a `status` of `'error'`, it will now render a "Try again" button, which will trigger the `retryLastUserMessage` function.
*   **File (Modified):** `src/components/TopBar/StatusIndicator.tsx`
    *   **Action:** Make the status indicator clickable to open a new diagnostics modal.
*   **File (New):** `src/components/Modals/StatusModal.tsx`
    *   **Action:** Create a new modal that displays detailed connection status for *all* providers, not just the active one. It will perform health checks on-demand when opened.
*   **File (Modified):** `src/stores/modalStore.ts`
    *   **Action:** Add `'status'` to the list of valid `ModalType`s.

---

## Full File Implementations

Here are the complete files with the new implementations.

### **File: `src/types/index.ts`** (Modified)
```typescript
export type ProviderID = 'openai' | 'anthropic' | 'google' | 'groq' | 'deepseek' | 'openrouter' | 'ollama';

export interface ProviderConfig {
  apiKey?: string;
  baseURL?: string;
  models: Model[];
}

export interface Model {
  id: string;
  name: string;
  description?: string;
  contextLength?: number;
}

export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  status?: 'sent' | 'error' | 'streaming';
}

export interface Conversation {
  id: string;
  title: string;
  messages: Message[];
  createdAt: string;
}

export interface ToastData {
  id: number;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
}

export type AdobeTheme = {
    baseFontFamily: string;
    baseFontSize: number;
    baseFontColor: string;
    backgroundColor: string;
};
```

### **File: `src/stores/modalStore.ts`** (Modified)
```typescript
import { create } from 'zustand';

type ModalType = 'settings' | 'provider' | 'history' | 'status' | 'error';

interface ModalState {
  activeModal: ModalType | null;
  modalData: any;
  openModal: (modal: ModalType, data?: any) => void;
  closeModal: () => void;
}

export const useModalStore = create<ModalState>((set) => ({
  activeModal: null,
  modalData: null,
  openModal: (modal, data = null) => set({ activeModal: modal, modalData: data }),
  closeModal: () => set({ activeModal: null, modalData: null }),
}));
```

### **File: `src/stores/chatStore.ts`** (Modified)
```typescript
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { v4 as uuidv4 } from 'uuid';
import { Conversation, Message, ProviderID } from '../types';
import { getProviderApi } from '../services/providerFactory';
import { useToastStore } from './toastStore';

interface ChatState {
  conversations: Record<string, Conversation>;
  currentConversationId: string | null;
  isLoading: boolean;
  addMessage: (message: Omit<Message, 'id' | 'timestamp'>, conversationId: string) => string;
  updateMessageStatus: (messageId: string, status: Message['status']) => void;
  appendTokenToMessage: (messageId: string, token: string) => void;
  sendChatMessage: (content: string) => Promise<void>;
  retryLastUserMessage: () => Promise<void>;
  startNewConversation: () => void;
  setCurrentConversationId: (id: string) => void;
  clearAllConversations: () => void;
}

const createNewConversation = (): Conversation => ({
  id: uuidv4(),
  title: 'New Chat',
  messages: [],
  createdAt: new Date().toISOString(),
});

export const useChatStore = create<ChatState>()(
  persist(
    (set, get) => ({
      conversations: {},
      currentConversationId: null,
      isLoading: false,

      addMessage: (messageContent, conversationId) => {
        const newMessage: Message = {
          ...messageContent,
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          status: 'sent',
        };
        set((state) => {
          const conversation = state.conversations[conversationId];
          return {
            conversations: {
              ...state.conversations,
              [conversationId]: {
                ...conversation,
                messages: [...conversation.messages, newMessage],
              },
            },
          };
        });
        return newMessage.id;
      },
      
      updateMessageStatus: (messageId, status) => {
        set(state => {
            const { currentConversationId, conversations } = state;
            if (!currentConversationId || !conversations[currentConversationId]) return {};
            const messages = conversations[currentConversationId].messages;
            const msgIndex = messages.findIndex(m => m.id === messageId);
            if (msgIndex === -1) return {};
            
            const updatedMessages = [...messages];
            updatedMessages[msgIndex] = { ...updatedMessages[msgIndex], status };
            
            return {
                conversations: {
                    ...state.conversations,
                    [currentConversationId]: {
                        ...conversations[currentConversationId],
                        messages: updatedMessages
                    }
                }
            }
        });
      },

      appendTokenToMessage: (messageId, token) => {
          set(state => {
              const { currentConversationId, conversations } = state;
              if (!currentConversationId || !conversations[currentConversationId]) return {};
              
              const messages = conversations[currentConversationId].messages;
              const msgIndex = messages.findIndex(m => m.id === messageId);
              if (msgIndex === -1) return {};

              const updatedMessages = [...messages];
              const targetMessage = updatedMessages[msgIndex];

              updatedMessages[msgIndex] = { ...targetMessage, content: targetMessage.content + token };
              
              return {
                  conversations: {
                      ...state.conversations,
                      [currentConversationId]: {
                          ...conversations[currentConversationId],
                          messages: updatedMessages
                      }
                  }
              }
          })
      },

      sendChatMessage: async (content: string) => {
        let { currentConversationId } = get();
        
        if (!currentConversationId) {
            const newConvo = createNewConversation();
            set(state => ({ 
                conversations: { ...state.conversations, [newConvo.id]: newConvo },
                currentConversationId: newConvo.id
            }));
            currentConversationId = newConvo.id;
        }

        get().addMessage({ role: 'user', content }, currentConversationId!);
        const assistantMsgId = get().addMessage({ role: 'assistant', content: '' }, currentConversationId!);
        get().updateMessageStatus(assistantMsgId, 'streaming');
        set({ isLoading: true });
        
        try {
          const { useSettingsStore } = await import('./settingsStore');
          const { selectedProvider, selectedModel } = useSettingsStore.getState();
          const api = getProviderApi(selectedProvider as ProviderID);
          const messages = get().conversations[currentConversationId!].messages.slice(0, -1);

          const stream = api.chat(messages, selectedModel);
          for await (const chunk of stream) {
            get().appendTokenToMessage(assistantMsgId, chunk);
          }
          get().updateMessageStatus(assistantMsgId, 'sent');
        } catch (error: any) {
            get().updateMessageStatus(assistantMsgId, 'error');
            get().appendTokenToMessage(assistantMsgId, `**Error:** ${error.message}`);
            useToastStore.getState().addToast({ message: error.message, type: 'error' });
        } finally {
            set({ isLoading: false });
        }
      },
      
      retryLastUserMessage: async () => {
          const { currentConversationId, conversations } = get();
          if (!currentConversationId || !conversations[currentConversationId]) return;
          
          const messages = conversations[currentConversationId].messages;
          const lastAssistantMsgIndex = messages.map(m => m.role).lastIndexOf('assistant');
          if (lastAssistantMsgIndex === -1 || messages[lastAssistantMsgIndex].status !== 'error') return;
          
          const userMessageToRetry = messages[lastAssistantMsgIndex -1];
          if (userMessageToRetry?.role !== 'user') return;
          
          // Remove the failed assistant message
          const updatedMessages = messages.slice(0, lastAssistantMsgIndex);
          set(state => ({
              conversations: {
                  ...state.conversations,
                  [currentConversationId]: { ...conversations[currentConversationId], messages: updatedMessages }
              }
          }));
          
          // Resend
          get().sendChatMessage(userMessageToRetry.content);
      },

      startNewConversation: () => {
        const newConvo = createNewConversation();
        set(state => ({
            conversations: { ...state.conversations, [newConvo.id]: newConvo },
            currentConversationId: newConvo.id
        }));
      },
      
      setCurrentConversationId: (id) => set({ currentConversationId: id }),
      
      clearAllConversations: () => set({ conversations: {}, currentConversationId: null }),

    }),
    {
      name: 'sahai-chat-storage',
    }
  )
);
```

### **File (New): `src/components/Common/SearchableSelect.tsx`**
```typescript
import React, { useState, useRef, useEffect, useMemo } from 'react';
import { ChevronDown, Search } from 'lucide-react';
import { Model } from '../../types';

interface SearchableSelectProps {
  options: Model[];
  value: string | null;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
}

const SearchableSelect: React.FC<SearchableSelectProps> = ({
  options,
  value,
  onChange,
  placeholder = 'Select a model...',
  disabled = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const containerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const selectedOption = useMemo(() => options.find(opt => opt.id === value), [options, value]);

  const filteredOptions = useMemo(() =>
    options.filter(opt =>
      opt.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      opt.id.toLowerCase().includes(searchQuery.toLowerCase())
    ), [options, searchQuery]);

  // Close dropdown on click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
        if (!isOpen) return;
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                setFocusedIndex(prev => Math.min(prev + 1, filteredOptions.length - 1));
                break;
            case 'ArrowUp':
                e.preventDefault();
                setFocusedIndex(prev => Math.max(prev - 1, 0));
                break;
            case 'Enter':
                e.preventDefault();
                if (focusedIndex >= 0 && filteredOptions[focusedIndex]) {
                    handleSelect(filteredOptions[focusedIndex]);
                }
                break;
            case 'Escape':
                setIsOpen(false);
                break;
        }
    };
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, focusedIndex, filteredOptions]);


  const handleSelect = (option: Model) => {
    onChange(option.id);
    setSearchQuery('');
    setIsOpen(false);
  };
  
  const handleOpen = () => {
    if (disabled) return;
    setIsOpen(true);
    setTimeout(() => inputRef.current?.focus(), 0);
  }

  return (
    <div ref={containerRef} className="relative w-full">
      <button
        onClick={handleOpen}
        disabled={disabled}
        className="flex items-center justify-between w-full px-2 py-1 text-left bg-adobe-secondary rounded border border-transparent hover:border-adobe disabled:opacity-50"
      >
        <span className="truncate text-xs">{selectedOption?.name || placeholder}</span>
        <ChevronDown size={14} className="text-gray-400" />
      </button>

      {isOpen && (
        <div className="absolute z-10 w-full mt-1 bg-adobe-bg border border-adobe rounded-md shadow-lg max-h-60 overflow-y-auto">
          <div className="p-2 border-b border-adobe sticky top-0 bg-adobe-bg">
            <div className="relative">
                <Search size={14} className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400" />
                <input
                    ref={inputRef}
                    type="text"
                    value={searchQuery}
                    onChange={e => setSearchQuery(e.target.value)}
                    placeholder="Search models..."
                    className="w-full pl-7 pr-2 py-1 bg-adobe-secondary border border-adobe rounded text-xs focus:outline-none"
                />
            </div>
          </div>
          <ul className="py-1">
            {filteredOptions.length > 0 ? (
              filteredOptions.map((option, index) => (
                <li
                  key={option.id}
                  onClick={() => handleSelect(option)}
                  onMouseEnter={() => setFocusedIndex(index)}
                  className={`px-3 py-1.5 text-xs cursor-pointer ${
                    index === focusedIndex ? 'bg-blue-600 text-white' : 'hover:bg-adobe-secondary'
                  } ${value === option.id ? 'font-bold' : ''}`}
                >
                  {option.name}
                </li>
              ))
            ) : (
              <li className="px-3 py-1.5 text-xs text-gray-500">No results found</li>
            )}
          </ul>
        </div>
      )}
    </div>
  );
};

export default SearchableSelect;
```

### **File: `src/components/TopBar/TopBar.tsx`** (Modified)
```typescript
import React from 'react';
import { Plus, History, Settings } from 'lucide-react';
import { useModalStore } from '../../stores/modalStore';
import { useChatStore } from '../../stores/chatStore';
import StatusIndicator from './StatusIndicator';
import { useSettingsStore } from '../../stores/settingsStore';
import SearchableSelect from '../Common/SearchableSelect';

const TopBar: React.FC = () => {
  const openModal = useModalStore((s) => s.openModal);
  const startNewConversation = useChatStore(s => s.startNewConversation);
  const { 
      selectedProvider, 
      selectedModel, 
      setSelectedModel,
      providers
  } = useSettingsStore();
  
  const modelsForProvider = providers[selectedProvider]?.models || [];

  return (
    <header className="flex items-center justify-between p-2 border-b border-adobe bg-adobe-secondary flex-shrink-0 gap-2">
      <div className="flex items-center gap-2 flex-shrink min-w-0">
        <StatusIndicator />
        <div className="flex flex-col gap-1 min-w-0">
            <p className="text-xs font-bold truncate capitalize">{selectedProvider}</p>
            <SearchableSelect 
                options={modelsForProvider}
                value={selectedModel}
                onChange={setSelectedModel}
                placeholder={modelsForProvider.length === 0 ? "No models found" : "Select a model"}
                disabled={modelsForProvider.length === 0}
            />
        </div>
      </div>
      <div className="flex items-center gap-1 flex-shrink-0">
        <button onClick={startNewConversation} className="p-1.5 rounded hover:bg-adobe-bg" aria-label="New Chat"><Plus size={16} /></button>
        <button onClick={() => openModal('history')} className="p-1.5 rounded hover:bg-adobe-bg" aria-label="Chat History"><History size={16} /></button>
        <button onClick={() => openModal('settings')} className="p-1.5 rounded hover:bg-adobe-bg" aria-label="Settings"><Settings size={16} /></button>
      </div>
    </header>
  );
};

export default TopBar;
```

### **File: `src/components/TopBar/StatusIndicator.tsx`** (Modified)
```typescript
import React, { useState, useEffect, useCallback } from 'react';
import { Loader2 } from 'lucide-react';
import { useSettingsStore } from '../../stores/settingsStore';
import { getProviderApi } from '../../services/providerFactory';
import { ProviderID } from '../../types';
import { useModalStore } from '../../stores/modalStore';

type Status = 'loading' | 'ok' | 'error' | 'idle';

const StatusIndicator: React.FC = () => {
    const selectedProvider = useSettingsStore(s => s.selectedProvider);
    const openModal = useModalStore(s => s.openModal);
    const [status, setStatus] = useState<Status>('idle');
    const [latency, setLatency] = useState<number | null>(null);

    const checkConnection = useCallback(async () => {
      if (!selectedProvider) {
          setStatus('idle');
          return;
      }
      setStatus('loading');
      const start = Date.now();
      try {
        const api = getProviderApi(selectedProvider as ProviderID);
        await api.getModels();
        setLatency(Date.now() - start);
        setStatus('ok');
      } catch (e) {
        setStatus('error');
        setLatency(null);
      }
    }, [selectedProvider]);

    useEffect(() => {
        checkConnection();
        const interval = setInterval(checkConnection, 30000);
        return () => clearInterval(interval);
    }, [checkConnection]);

    const colorMap: Record<Status, string> = {
        idle: 'bg-gray-400',
        loading: 'bg-yellow-500 animate-pulse',
        ok: 'bg-green-500',
        error: 'bg-red-500',
    };
    
    const titleMap: Record<Status, string> = {
        idle: 'Status: Idle',
        loading: 'Status: Checking connection...',
        ok: `Status: Connected | Latency: ${latency ?? 'N/A'}ms. Click for details.`,
        error: 'Status: Connection failed. Click for details.',
    };

    return (
        <div 
            className="flex items-center gap-2 cursor-pointer" 
            title={titleMap[status]}
            onClick={() => openModal('status')}
        >
            {status === 'loading' ? 
              <Loader2 size={14} className="animate-spin text-yellow-500" /> : 
              <div className={`w-3 h-3 rounded-full ${colorMap[status]}`} />
            }
        </div>
    );
};

export default StatusIndicator;
```

### **File: `src/components/InputArea/InputArea.tsx`** (Modified)
```typescript
import React, { useState, useRef, KeyboardEvent, useEffect } from 'react';
import { Send, Paperclip, Mic, Loader2 } from 'lucide-react';
import { useChatStore } from '../../stores/chatStore';
import { useToastStore } from '../../stores/toastStore';

const InputArea: React.FC = () => {
  const [text, setText] = useState('');
  const [isListening, setIsListening] = useState(false);
  const { sendChatMessage, isLoading } = useChatStore();
  const addToast = useToastStore(s => s.addToast);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const recognitionRef = useRef<any>(null); // For SpeechRecognition instance

  const handleSend = () => {
    if (text.trim() && !isLoading) {
      sendChatMessage(text.trim());
      setText('');
      setTimeout(() => textareaRef.current?.focus(), 0);
    }
  };
  
  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        handleSend();
    }
  };

  const handleVoiceInput = () => {
    if (isListening) {
        recognitionRef.current?.stop();
        setIsListening(false);
        return;
    }

    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (!SpeechRecognition) {
        addToast({ message: "Voice recognition is not supported in this browser.", type: 'error' });
        return;
    }

    recognitionRef.current = new SpeechRecognition();
    recognitionRef.current.continuous = true;
    recognitionRef.current.interimResults = true;
    recognitionRef.current.lang = 'en-US';

    recognitionRef.current.onstart = () => setIsListening(true);
    recognitionRef.current.onend = () => setIsListening(false);
    recognitionRef.current.onerror = (event: any) => {
        addToast({ message: `Voice recognition error: ${event.error}`, type: 'error' });
        setIsListening(false);
    };

    recognitionRef.current.onresult = (event: any) => {
      let interimTranscript = '';
      for (let i = event.resultIndex; i < event.results.length; ++i) {
        if (event.results[i].isFinal) {
          setText(prev => prev + event.results[i][0].transcript + ' ');
        } else {
          interimTranscript += event.results[i][0].transcript;
        }
      }
    };
    
    recognitionRef.current.start();
  };

  useEffect(() => {
    if(textareaRef.current) {
        textareaRef.current.style.height = 'auto';
        textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [text]);

  return (
    <div className="p-2 border-t border-adobe bg-adobe-bg flex-shrink-0">
      <div className="flex items-start gap-2 p-2 rounded-md bg-adobe-secondary">
        <button className="p-1.5 rounded" aria-label="Attach File"><Paperclip size={18} /></button>
        <button 
          onClick={handleVoiceInput}
          className={`p-1.5 rounded ${isListening ? 'bg-red-500 text-white' : ''}`} 
          aria-label="Voice Input"
        >
          <Mic size={18} />
        </button>
        <textarea
          ref={textareaRef}
          value={text}
          onChange={(e) => setText(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={isListening ? "Listening..." : "Type a message or use voice input..."}
          className="flex-grow bg-transparent focus:outline-none resize-none max-h-48 overflow-y-auto"
          rows={1}
          maxLength={4000}
          disabled={isLoading}
        />
        <button onClick={handleSend} disabled={isLoading || !text.trim()} className="p-1.5 rounded bg-blue-600 text-white disabled:bg-gray-500 self-end">
          {isLoading ? <Loader2 size={18} className="animate-spin" /> : <Send size={18} />}
        </button>
      </div>
    </div>
  );
};

export default InputArea;
```

### **File: `src/components/ChatInterface/MessageBubble.tsx`** (Modified)
```typescript
import React from 'react';
import { Message } from '../../types';
import CodeBlock from './CodeBlock';
import { User, Bot, RefreshCw } from 'lucide-react';
import { useChatStore } from '../../stores/chatStore';

const MessageBubble: React.FC<{ message: Message }> = ({ message }) => {
  const isUser = message.role === 'user';
  const { retryLastUserMessage } = useChatStore();
  
  const parts = message.content.split(/(```[\w\s]*\n[\s\S]*?\n```)/g);

  return (
    <div className={`flex items-start gap-3 ${isUser ? 'justify-end' : 'self-start'}`}>
      {!isUser && <div className="flex-shrink-0 w-8 h-8 rounded-full bg-adobe-secondary flex items-center justify-center"><Bot size={18} /></div>}
      <div className={`max-w-[85%] rounded-lg p-3 ${
          isUser 
            ? 'bg-blue-600 text-white' 
            : message.status === 'error' ? 'bg-red-100 text-red-800' : 'bg-adobe-secondary'
      }`}>
        {parts.map((part, index) => {
          if (part.startsWith('```')) {
            const match = part.match(/```(\w*)\n([\s\S]*?)\n```/);
            if (match) {
              const lang = match || 'plaintext';
              const code = match || '';
              return <CodeBlock key={index} code={code} language={lang} />;
            }
          }
          // Basic markdown for bolding error messages
          return <p key={index} className="whitespace-pre-wrap" dangerouslySetInnerHTML={{__html: part.replace(/\*\*(.*?)\*\*/g, '<b>$1</b>')}}></p>;
        })}
        
        {message.status === 'error' && !isUser && (
             <div className="mt-2 pt-2 border-t border-red-300 flex justify-end">
                 <button 
                    onClick={retryLastUserMessage} 
                    className="flex items-center gap-2 text-xs font-semibold text-red-700 hover:text-red-900"
                >
                    <RefreshCw size={12} />
                    Try again
                 </button>
            </div>
        )}
      </div>
    </div>
  );
};

export default MessageBubble;
```

### **File (New): `src/components/Modals/StatusModal.tsx`**
```typescript
import React, { useState, useEffect } from 'react';
import Modal from '../Common/Modal';
import { getProviderApi } from '../../services/providerFactory';
import { ProviderID } from '../../types';
import { Loader2, CheckCircle, XCircle, AlertTriangle, RefreshCw } from 'lucide-react';

const PROVIDER_IDS: ProviderID[] = ['openai', 'anthropic', 'google', 'groq', 'deepseek', 'openrouter', 'ollama'];

type StatusDetail = {
  status: 'loading' | 'success' | 'error' | 'unconfigured';
  latency?: number;
  modelsCount?: number;
  error?: string;
};

const StatusRow: React.FC<{ providerId: ProviderID }> = ({ providerId }) => {
    const [detail, setDetail] = useState<StatusDetail>({ status: 'loading' });

    const checkStatus = async () => {
        setDetail({ status: 'loading' });
        const start = Date.now();
        try {
            const api = getProviderApi(providerId);
            const models = await api.getModels();
            if (models.length > 0) {
                 setDetail({
                    status: 'success',
                    latency: Date.now() - start,
                    modelsCount: models.length
                });
            } else {
                 setDetail({
                    status: 'error',
                    error: 'Connected, but no models found.'
                });
            }
        } catch (error: any) {
            const isAuthError = /API key/i.test(error.message);
             setDetail({
                status: isAuthError ? 'unconfigured' : 'error',
                error: isAuthError ? 'API key not set or invalid.' : 'Connection failed.'
            });
        }
    };
    
    useEffect(() => {
        checkStatus();
    }, [providerId]);

    const ICONS = {
        loading: <Loader2 size={16} className="animate-spin text-yellow-500" />,
        success: <CheckCircle size={16} className="text-green-500" />,
        error: <XCircle size={16} className="text-red-500" />,
        unconfigured: <AlertTriangle size={16} className="text-orange-500" />,
    };

    return (
        <tr className="border-b border-adobe">
            <td className="p-2 font-semibold capitalize">{providerId}</td>
            <td className="p-2 text-center">{ICONS[detail.status]}</td>
            <td className="p-2 text-xs">{detail.latency ? `${detail.latency} ms` : 'N/A'}</td>
            <td className="p-2 text-xs">{detail.modelsCount ?? 'N/A'}</td>
            <td className="p-2 text-xs text-gray-400">{detail.error ?? 'OK'}</td>
            <td className="p-2 text-center">
                <button onClick={checkStatus} title="Re-check status">
                    <RefreshCw size={14} className="hover:text-blue-500" />
                </button>
            </td>
        </tr>
    );
}


const StatusModal: React.FC = () => {
  return (
    <Modal title="System Connection Status">
      <div className="max-h-[60vh] overflow-y-auto">
        <table className="w-full text-sm text-left">
            <thead className="bg-adobe-secondary sticky top-0">
                <tr className="border-b-2 border-adobe">
                    <th className="p-2">Provider</th>
                    <th className="p-2 text-center">Status</th>
                    <th className="p-2">Latency</th>
                    <th className="p-2">Models</th>
                    <th className="p-2">Details</th>
                    <th className="p-2 text-center">Check</th>
                </tr>
            </thead>
            <tbody>
                {PROVIDER_IDS.map(id => <StatusRow key={id} providerId={id} />)}
            </tbody>
        </table>
        <p className="text-xs text-gray-500 mt-4">
            This panel shows the real-time status of each AI provider. Latency is measured by fetching the model list.
        </p>
      </div>
    </Modal>
  );
};

export default StatusModal;
```