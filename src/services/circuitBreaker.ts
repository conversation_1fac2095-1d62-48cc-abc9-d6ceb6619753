// Simple in-memory circuit breaker implementation
type CircuitState = 'CLOSED' | 'OPEN' | 'HALF_OPEN';

interface Circuit {
  state: CircuitState;
  failureCount: number;
  lastFailure: number | null;
  recoveryTimeout: number; // ms
  failureThreshold: number;
}

const circuits: Record<string, Circuit> = {};

export function getCircuit(id: string): Circuit {
  if (!circuits[id]) {
    circuits[id] = {
      state: 'CLOSED',
      failureCount: 0,
      lastFailure: null,
      recoveryTimeout: 30000, // 30 seconds
      failureThreshold: 3,
    };
  }
  return circuits[id];
}

export function trip(circuit: Circuit) {
  circuit.state = 'OPEN';
  circuit.lastFailure = Date.now();
  console.warn(`Circuit ${circuit} tripped to OPEN state.`);
}

export function reset(circuit: Circuit) {
  circuit.state = 'CLOSED';
  circuit.failureCount = 0;
  circuit.lastFailure = null;
  console.log(`Circuit ${circuit} reset to CLOSED state.`);
}

export function halfOpen(circuit: Circuit) {
  circuit.state = 'HALF_OPEN';
}

export async function withCircuitBreaker<T>(
  circuitId: string,
  fn: () => Promise<T>
): Promise<T> {
  const circuit = getCircuit(circuitId);

  if (circuit.state === 'OPEN') {
    if (circuit.lastFailure && Date.now() - circuit.lastFailure > circuit.recoveryTimeout) {
      halfOpen(circuit);
    } else {
      throw new Error(`Circuit for ${circuitId} is open.`);
    }
  }

  try {
    const result = await fn();
    if (circuit.state === 'HALF_OPEN' || circuit.failureCount > 0) {
      reset(circuit);
    }
    return result;
  } catch (err) {
    circuit.failureCount++;
    if (circuit.state === 'HALF_OPEN' || circuit.failureCount >= circuit.failureThreshold) {
      trip(circuit);
    }
    throw err;
  }
}
