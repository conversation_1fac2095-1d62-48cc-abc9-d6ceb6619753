import { getSecureCredential } from "../utils/cep";
import { ProviderID } from "../types";

// This is a placeholder for a generic fetch function.
// In a real app, this would handle headers, errors, and different API structures.
export async function makeApiRequest(endpoint: string, provider: ProviderID, options: RequestInit) {
    const apiKey = getSecureCredential(provider);
    
    const headers = new Headers(options.headers || {});
    headers.set('Content-Type', 'application/json');

    // Add authentication based on provider specifics
    // This is a simplified example.
    if (apiKey) {
        if (provider === 'google') {
            endpoint = `${endpoint}?key=${apiKey}`;
        } else if (provider === 'anthropic') {
            headers.set('x-api-key', apiKey);
            headers.set('anthropic-version', '2023-06-01');
        }
        else { // OpenAI, Groq, DeepSeek, OpenRouter
            headers.set('Authorization', `Bearer ${apiKey}`);
        }
    }

    const response = await fetch(endpoint, {
        ...options,
        headers,
    });

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: response.statusText }));
        throw new Error(`API Error (${response.status}): ${errorData.message || 'Unknown error'}`);
    }

    return response.json();
}
