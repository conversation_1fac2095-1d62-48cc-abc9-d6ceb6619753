import { ProviderID, Model, Message, ProviderConfig } from '../types';
import { getSecureCredential } from '../utils/cep';
import { useSettingsStore } from '../stores/settingsStore';

/**
 * Standard interface for all AI provider services.
 * This ensures that the application can interact with any provider
 * in a consistent way.
 */
export interface IProvider {
  /**
   * Fetches the list of available models from the provider.
   * @returns A promise that resolves to an array of Model objects.
   */
  getModels(): Promise<Model[]>;

  /**
   * Sends a chat conversation to the provider and streams the response.
   * @param messages The array of messages in the conversation.
   * @param modelId The ID of the model to use for the chat.
   * @returns An async generator that yields chunks of the response text.
   */
  chat(messages: Message[], modelId: string): AsyncGenerator<string>;
}

// --- Provider Implementations ---

/**
 * Handles providers with OpenAI-compatible APIs.
 * This includes OpenAI, Groq, and DeepSeek.
 */
class OpenAICompatibleProvider implements IProvider {
  constructor(protected providerId: ProviderID, protected baseUrl: string) {}

  async getModels(): Promise<Model[]> {
    const apiKey = getSecureCredential(this.providerId);
    if (!apiKey) return [];

    const response = await this.makeRequest('/models');
    // The model list is usually in a 'data' property
    const models = response.data || response;
    return models.map((model: any) => ({
      id: model.id,
      name: model.id,
      description: `Owned by ${model.owned_by || 'unknown'}`,
      contextLength: model.context_window || model.context_length,
    })).sort((a,b) => a.name.localeCompare(b.name));
  }

  async *chat(messages: Message[], modelId: string): AsyncGenerator<string> {
    const apiKey = getSecureCredential(this.providerId);
    if (!apiKey) {
      throw new Error(`API key for ${this.providerId} not found.`);
    }

    const body = JSON.stringify({
      model: modelId,
      messages: messages.map(({ role, content }) => ({ role, content })),
      stream: true,
    });

    const response = await this.makeRequest('/chat/completions', {
      method: 'POST',
      body,
    }, true);
    
    yield* this.processStream(response);
  }

  protected async makeRequest(endpoint: string, options: RequestInit = {}, stream = false) {
    const apiKey = getSecureCredential(this.providerId);
    const headers = new Headers(options.headers || {});
    headers.set('Content-Type', 'application/json');
    headers.set('Authorization', `Bearer ${apiKey}`);
    
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      ...options,
      headers,
    });

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`API Error (${response.status}): ${errorData.error?.message || response.statusText}`);
    }
    
    return stream ? response : response.json();
  }
  
  protected async *processStream(response: Response): AsyncGenerator<string> {
      const reader = response.body?.getReader();
      if (!reader) throw new Error('Failed to read stream.');
      const decoder = new TextDecoder();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n\n');

        for (const line of lines) {
            if (line.startsWith('data: ')) {
                const data = line.substring(6);
                if (data.trim() === '[DONE]') return;
                try {
                    const parsed = JSON.parse(data);
                    const content = parsed.choices[0]?.delta?.content;
                    if (content) {
                        yield content;
                    }
                } catch (e) {
                    console.error('Error parsing stream data chunk:', data);
                }
            }
        }
      }
  }
}

/**
 * Handles the Anthropic API (Claude models).
 */
class AnthropicProvider implements IProvider {
  private readonly BASE_URL = 'https://api.anthropic.com/v1';

  async getModels(): Promise<Model[]> {
    // Anthropic doesn't have a public models endpoint. Models are documented.
    // We list the most common ones here. This should be updated periodically.
    return Promise.resolve([
        { id: "claude-3-opus-20240229", name: "Claude 3 Opus", contextLength: 200000 },
        { id: "claude-3-sonnet-20240229", name: "Claude 3 Sonnet", contextLength: 200000 },
        { id: "claude-3-haiku-20240307", name: "Claude 3 Haiku", contextLength: 200000 },
        { id: "claude-2.1", name: "Claude 2.1", contextLength: 200000 },
        { id: "claude-2.0", name: "Claude 2.0", contextLength: 100000 },
    ]);
  }

  async *chat(messages: Message[], modelId: string): AsyncGenerator<string> {
    const apiKey = getSecureCredential('anthropic');
    if (!apiKey) throw new Error('API key for Anthropic not found.');
    
    // Anthropic separates the system prompt from the messages
    const systemPrompt = messages.find(m => m.role === 'system')?.content;
    const userMessages = messages.filter(m => m.role !== 'system');

    const body = JSON.stringify({
      model: modelId,
      messages: userMessages.map(({ role, content }) => ({ role, content })),
      ...(systemPrompt && { system: systemPrompt }),
      stream: true,
      max_tokens: 4096, // Recommended value
    });

    const response = await fetch(`${this.BASE_URL}/messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01',
      },
      body,
    });
    
    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`API Error (${response.status}): ${errorData.error?.message || response.statusText}`);
    }

    // Process Anthropic's Server-Sent Events (SSE) stream
    const reader = response.body?.getReader();
    if (!reader) throw new Error('Failed to read stream.');
    const decoder = new TextDecoder();

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value);
      const lines = chunk.split('\n');
      for(const line of lines) {
          if (line.startsWith('data: ')) {
              try {
                  const data = JSON.parse(line.substring(6));
                  if (data.type === 'content_block_delta' && data.delta.type === 'text_delta') {
                      yield data.delta.text;
                  }
              } catch(e) {
                  console.error('Error parsing Anthropic stream chunk:', line);
              }
          }
      }
    }
  }
}

/**
 * Handles the Google Gemini API.
 */
class GoogleGeminiProvider implements IProvider {
    private readonly BASE_URL = 'https://generativelanguage.googleapis.com/v1beta/models';

    async getModels(): Promise<Model[]> {
        const apiKey = getSecureCredential('google');
        if (!apiKey) return [];
        
        const response = await fetch(`${this.BASE_URL}?key=${apiKey}`);
        const data = await response.json();
        
        return data.models
            .filter((m: any) => m.supportedGenerationMethods.includes("generateContent"))
            .map((model: any) => ({
                id: model.name,
                name: model.displayName,
                description: model.description,
                contextLength: model.inputTokenLimit,
            })).sort((a,b) => a.name.localeCompare(b.name));
    }

    async *chat(messages: Message[], modelId: string): AsyncGenerator<string> {
        const apiKey = getSecureCredential('google');
        if (!apiKey) throw new Error('API key for Google Gemini not found.');

        // Gemini uses a different message format ('contents')
        const contents = messages.map(msg => ({
            role: msg.role === 'assistant' ? 'model' : 'user',
            parts: [{ text: msg.content }]
        }));

        const body = JSON.stringify({ contents });
        const modelName = modelId.split('/').pop(); // API wants 'gemini-pro', not 'models/gemini-pro'

        const response = await fetch(`${this.BASE_URL}/${modelName}:streamGenerateContent?key=${apiKey}&alt=sse`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body,
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(`API Error (${response.status}): ${errorData.error?.message || response.statusText}`);
        }

        const reader = response.body?.getReader();
        if (!reader) throw new Error('Failed to read stream.');
        const decoder = new TextDecoder();

        while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            const chunk = decoder.decode(value);
            const lines = chunk.split('\n');
            for(const line of lines) {
                if(line.startsWith('data: ')) {
                    try {
                        const data = JSON.parse(line.substring(6));
                        const text = data.candidates?.[0]?.content?.parts?.[0]?.text;
                        if (text) {
                            yield text;
                        }
                    } catch (e) {
                        // Ignore parsing errors which can happen with incomplete chunks
                    }
                }
            }
        }
    }
}

/**
 * Handles OpenRouter, which is mostly OpenAI-compatible but needs custom headers.
 */
class OpenRouterProvider extends OpenAICompatibleProvider {
    constructor() {
        super('openrouter', 'https://openrouter.ai/api/v1');
    }

    // Override makeRequest to add custom headers
    protected async makeRequest(endpoint: string, options: RequestInit = {}, stream = false) {
        const apiKey = getSecureCredential(this.providerId);
        const headers = new Headers(options.headers || {});
        headers.set('Content-Type', 'application/json');
        headers.set('Authorization', `Bearer ${apiKey}`);
        // OpenRouter specific headers
        headers.set('HTTP-Referer', 'https://sahai.com/cep'); // Replace with your site
        headers.set('X-Title', 'SahAI CEP Extension'); // Replace with your app name

        const response = await fetch(`${this.baseUrl}${endpoint}`, {
            ...options,
            headers,
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(`API Error (${response.status}): ${errorData.error?.message || response.statusText}`);
        }
        
        return stream ? response : response.json();
    }
}


/**
 * Handles a local Ollama instance.
 */
class OllamaProvider implements IProvider {
  constructor(private baseUrl: string) {}

  async getModels(): Promise<Model[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tags`);
      if (!response.ok) return [];
      const data = await response.json();
      return data.models.map((model: any) => ({
        id: model.name,
        name: model.name,
        description: `Size: ${(model.size / 1e9).toFixed(2)} GB`,
      })).sort((a,b) => a.name.localeCompare(b.name));
    } catch (e) {
      console.error("Failed to connect to Ollama:", e);
      return []; // Return empty if Ollama isn't running
    }
  }

  async *chat(messages: Message[], modelId: string): AsyncGenerator<string> {
    const body = JSON.stringify({
      model: modelId,
      messages: messages.map(({ role, content }) => ({ role, content })),
      stream: true,
    });

    const response = await fetch(`${this.baseUrl}/api/chat`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body,
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Ollama Error (${response.status}): ${errorData.error || response.statusText}`);
    }

    const reader = response.body?.getReader();
    if (!reader) throw new Error('Failed to read stream.');
    const decoder = new TextDecoder();

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      
      const chunk = decoder.decode(value);
      // Ollama streams JSON objects separated by newlines
      const lines = chunk.split('\n');
      for (const line of lines) {
        if (line.trim()) {
            try {
                const parsed = JSON.parse(line);
                if (parsed.message?.content) {
                    yield parsed.message.content;
                }
                if(parsed.done) return;
            } catch (e) {
                console.error("Error parsing Ollama stream chunk:", line);
            }
        }
      }
    }
  }
}

/**
 * Factory function to get an instance of the correct provider API service.
 * @param providerId The ID of the provider to get.
 * @returns An instance of a class that implements the IProvider interface.
 */
export function getProviderApi(providerId: ProviderID): IProvider {
  const providerConfig = useSettingsStore.getState().providers[providerId];

  switch (providerId) {
    case 'openai':
      return new OpenAICompatibleProvider(providerId, 'https://api.openai.com/v1');
    case 'groq':
      return new OpenAICompatibleProvider(providerId, 'https://api.groq.com/openai/v1');
    case 'deepseek':
      return new OpenAICompatibleProvider(providerId, 'https://api.deepseek.com');
    case 'anthropic':
      return new AnthropicProvider();
    case 'google':
      return new GoogleGeminiProvider();
    case 'openrouter':
        return new OpenRouterProvider();
    case 'ollama':
      // Use configured base URL or default
      const baseUrl = providerConfig?.baseURL || 'http://localhost:11434';
      return new OllamaProvider(baseUrl);
    default:
      // This is a type-safe way to ensure all providers are handled.
      // If a new ProviderID is added, TypeScript will error here.
      const exhaustiveCheck: never = providerId;
      throw new Error(`Provider ${exhaustiveCheck} not implemented.`);
  }
}