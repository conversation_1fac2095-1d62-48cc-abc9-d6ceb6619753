export type ProviderID = 'openai' | 'anthropic' | 'google' | 'groq' | 'deepseek' | 'openrouter' | 'ollama';

export interface ProviderConfig {
  apiKey?: string;
  baseURL?: string;
  models: Model[];
}

export interface Model {
  id: string;
  name: string;
  description?: string;
  contextLength?: number;
}

export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  status?: 'sent' | 'error' | 'streaming';
}

export interface Conversation {
  id: string;
  title: string;
  messages: Message[];
  createdAt: string;
}

export interface ToastData {
  id: number;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
}

export type AdobeTheme = {
    baseFontFamily: string;
    baseFontSize: number;
    baseFontColor: string;
    backgroundColor: string;
};

// Web Speech API declarations
declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}
