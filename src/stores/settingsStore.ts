import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { ProviderID, ProviderConfig, AdobeTheme, Model } from '../types';
import { getSecureCredential, setSecureCredential } from '../utils/cep';
import { getProviderApi } from '../services/providerFactory';
import { useToastStore } from './toastStore';

interface SettingsState {
  providers: Record<ProviderID, ProviderConfig>;
  selectedProvider: ProviderID;
  selectedModel: string;
  theme: 'light' | 'dark' | 'auto';
  adobeTheme: AdobeTheme | null;
  setProviderApiKey: (providerId: ProviderID, apiKey: string) => void;
  setOllamaBaseUrl: (baseUrl: string) => void;
  setSelectedProvider: (providerId: ProviderID) => void;
  setSelectedModel: (modelId: string) => void;
  setTheme: (theme: 'light' | 'dark' | 'auto', adobeTheme?: AdobeTheme) => void;
  applyTheme: () => void;
  refreshProviderModels: (providerId: ProviderID) => Promise<void>;
}

const initialProviders: Record<ProviderID, ProviderConfig> = {
  openai: { models: [] },
  anthropic: { models: [] },
  google: { models: [] },
  groq: { models: [] },
  deepseek: { models: [] },
  openrouter: { models: [] },
  ollama: { baseURL: 'http://localhost:11434', models: [] },
};

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set, get) => ({
      providers: initialProviders,
      selectedProvider: 'openai',
      selectedModel: '',
      theme: 'auto',
      adobeTheme: null,

      setProviderApiKey: (providerId, apiKey) => {
        setSecureCredential(providerId, apiKey);
        // Automatically refresh models after setting a new key.
        get().refreshProviderModels(providerId);
      },

      setOllamaBaseUrl: (baseUrl: string) => {
        set((state) => ({
          providers: {
            ...state.providers,
            ollama: { ...state.providers.ollama, baseURL: baseUrl },
          },
        }));
      },

      setSelectedProvider: (providerId) => {
        set({ selectedProvider: providerId, selectedModel: '' });
        // Refresh models when switching to a provider if the list is empty
        if (get().providers[providerId].models.length === 0) {
            get().refreshProviderModels(providerId);
        }
      },

      setSelectedModel: (modelId) => set({ selectedModel: modelId }),

      setTheme: (theme, adobeTheme) => set({ theme, ...(adobeTheme && { adobeTheme }) }),

      applyTheme: () => {
        const { theme, adobeTheme } = get();
        const root = document.documentElement;
        if ((theme === 'auto' && adobeTheme) || theme !== 'auto') {
            const isDark = theme === 'dark' || (adobeTheme && parseInt(adobeTheme.backgroundColor.substring(1), 16) < 0x808080);
            if (isDark) {
                root.style.setProperty('--adobe-bg-color', '#323232');
                root.style.setProperty('--adobe-text-color', '#F0F0F0');
                root.style.setProperty('--adobe-secondary-bg-color', '#3C3C3C');
                root.style.setProperty('--adobe-border-color', '#4A4A4A');
                root.style.setProperty('--adobe-scrollbar-thumb-color', '#555555');
                root.style.setProperty('--adobe-scrollbar-track-color', '#323232');
            } else {
                root.style.setProperty('--adobe-bg-color', '#F5F5F5');
                root.style.setProperty('--adobe-text-color', '#1a1a1a');
                root.style.setProperty('--adobe-secondary-bg-color', '#EAEAEA');
                root.style.setProperty('--adobe-border-color', '#D3D3D3');
                root.style.setProperty('--adobe-scrollbar-thumb-color', '#C1C1C1');
                root.style.setProperty('--adobe-scrollbar-track-color', '#F5F5F5');
            }
        }
      },

      refreshProviderModels: async (providerId) => {
        try {
          const api = getProviderApi(providerId);
          const models: Model[] = await api.getModels();
          set((state) => ({
            providers: {
              ...state.providers,
              [providerId]: { ...state.providers[providerId], models },
            },
          }));
          // Auto-select the first model if none is selected
          if (!get().selectedModel && models.length > 0) {
            get().setSelectedModel(models[0].id);
          }
        } catch (error: any) {
          console.error(`Failed to fetch models for ${providerId}:`, error);
          useToastStore.getState().addToast({
            message: `Could not fetch models for ${providerId}. Check API key and connection.`,
            type: 'error',
          });
          // Clear stale models on error
           set((state) => ({
            providers: {
              ...state.providers,
              [providerId]: { ...state.providers[providerId], models: [] },
            },
          }));
        }
      },
    }),
    {
      name: 'sahai-settings-storage',
      partialize: (state) => ({
        selectedProvider: state.selectedProvider,
        selectedModel: state.selectedModel,
        theme: state.theme,
        providers: { ollama: state.providers.ollama }, // Persist Ollama URL
      }),
    }
  )
);

// Load API keys from secure storage on startup
Object.keys(initialProviders).forEach(id => {
    const key = getSecureCredential(id as ProviderID);
    if (key) {
        // You might trigger a state update or just let services use getSecureCredential directly
        console.log(`API key loaded for ${id}`);
    }
});
