import { create } from 'zustand';
import { ToastData } from '../types';

interface ToastState {
  toasts: ToastData[];
  addToast: (toast: Omit<ToastData, 'id'>) => void;
  removeToast: (id: number) => void;
}

export const useToastStore = create<ToastState>((set) => ({
  toasts: [],
  addToast: (toast) => {
    const newToast = { ...toast, id: Date.now() };
    set((state) => ({ toasts: [...state.toasts, newToast] }));
    
    setTimeout(() => {
      set((state) => ({ toasts: state.toasts.filter(t => t.id !== newToast.id) }));
    }, toast.duration || 5000);
  },
  removeToast: (id) => {
    set((state) => ({ toasts: state.toasts.filter((t) => t.id !== id) }));
  },
}));
