import { AdobeTheme, ProviderID } from "../types";
import { useSettingsStore } from '../stores/settingsStore';

// Declare the CSInterface object for TypeScript
declare const CSInterface: any;

let csInterface: any;

export const initializeCEP = () => {
  if (typeof CSInterface !== 'undefined') {
    csInterface = new CSInterface();
    syncTheme();
  } else {
    console.log("Running in browser, CEP features disabled.");
  }
};

/**
 * Evaluates an ExtendScript function from the main.jsx file.
 * @param script The string containing the script to be evaluated.
 * @param callback Optional callback function to handle the return value.
 */
export const evalScript = (script: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    if (!csInterface) {
      console.warn("evalScript called but CSInterface is not available.");
      reject("Not in a CEP environment.");
      return;
    }
    csInterface.evalScript(script, (result: any) => {
      try {
        // ExtendScript often returns strings, parse if it's JSON
        resolve(JSON.parse(result));
      } catch (e) {
        // If not JSON, return the raw result
        resolve(result);
      }
    });
  });
};

/**
 * Synchronizes the UI theme with the Adobe host application's theme.
 */
export const syncTheme = () => {
  if (!csInterface) return;
  
  const skinInfo = csInterface.getThemeInformation();
  const theme = {
    baseFontFamily: skinInfo.baseFontFamily,
    baseFontSize: skinInfo.baseFontSize,
    baseFontColor: `#${skinInfo.baseFontColor.color.hex}`,
    backgroundColor: `#${skinInfo.systemHighlightColor.color.hex}`,
  } as AdobeTheme

  const themeSlug = getThemeSlug(theme.backgroundColor);
  useSettingsStore.getState().setTheme(themeSlug, theme);
  
  csInterface.addEventListener("com.adobe.csxs.events.ThemeColorChanged", () => syncTheme());
};

const getThemeSlug = (bgColor: string): 'light' | 'dark' => {
  // A simple brightness calculation
  const color = bgColor.substring(1); // strip #
  const r = parseInt(color.substring(0, 2), 16);
  const g = parseInt(color.substring(2, 4), 16);
  const b = parseInt(color.substring(4, 6), 16);
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;
  return brightness > 125 ? 'light' : 'dark';
};

/**
 * Securely stores an API key. In a real-world scenario, this would use
 * more robust encryption, possibly leveraging OS-level secure storage via a native plugin.
 * For CEP, `window.localStorage` is sandboxed to the extension.
 */
export const setSecureCredential = (provider: ProviderID, apiKey: string) => {
  try {
    // Basic obfuscation for demonstration. NOT secure encryption.
    const encodedKey = btoa(apiKey);
    localStorage.setItem(`sahai_api_key_${provider}`, encodedKey);
  } catch (error) {
    console.error("Failed to store credential:", error);
  }
};

/**
 * Retrieves and decodes a stored API key.
 */
export const getSecureCredential = (provider: ProviderID): string | null => {
  try {
    const encodedKey = localStorage.getItem(`sahai_api_key_${provider}`);
    if (encodedKey) {
      return atob(encodedKey);
    }
    return null;
  } catch (error) {
    console.error("Failed to retrieve credential:", error);
    return null;
  }
};
