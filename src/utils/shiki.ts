import { getHighlighter, Highlighter, BUNDLED_LANGUAGES, Lang } from 'shiki';

let highlighter: Highlighter | null = null;

const supportedLanguages: Lang[] = [
  'javascript', 'typescript', 'tsx', 'html', 'css', 'json', 'xml', 
  'markdown', 'yaml', 'scss', 'less', 'python', 'swift', 'rust', 
  'go', 'java', 'php', 'ruby', 'shell', 'shellscript'
];

export async function initializeShiki(): Promise<Highlighter> {
  if (highlighter) {
    return highlighter;
  }
  
  highlighter = await getHighlighter({
    themes: ['github-dark'],
    langs: supportedLanguages,
  });
  
  return highlighter;
}

export async function highlightCode(code: string, lang: string): Promise<string> {
  const shiki = await initializeShiki();
  
  // Fallback for unsupported languages
  const language = shiki.getLoadedLanguages().includes(lang as Lang) ? lang : 'txt';

  return shiki.codeToHtml(code, { lang: language, theme: 'github-dark' });
}
