@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Default Light Theme Values */
  --adobe-bg-color: #F5F5F5;
  --adobe-text-color: #1a1a1a;
  --adobe-primary-color: #2680EB;
  --adobe-secondary-bg-color: #EAEAEA;
  --adobe-border-color: #D3D3D3;
  --adobe-scrollbar-thumb-color: #C1C1C1;
  --adobe-scrollbar-track-color: #F5F5F5;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  background-color: var(--adobe-bg-color);
  color: var(--adobe-text-color);
}

.bg-adobe-bg { background-color: var(--adobe-bg-color); }
.text-adobe-text { color: var(--adobe-text-color); }
.bg-adobe-secondary { background-color: var(--adobe-secondary-bg-color); }
.border-adobe { border-color: var(--adobe-border-color); }

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
::-webkit-scrollbar-track {
  background: var(--adobe-scrollbar-track-color);
}
::-webkit-scrollbar-thumb {
  background: var(--adobe-scrollbar-thumb-color);
  border-radius: 4px;
}
::-webkit-scrollbar-thumb:hover {
  background: #A8A8A8;
}
