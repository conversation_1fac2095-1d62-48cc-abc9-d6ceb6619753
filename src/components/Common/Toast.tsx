import React from 'react';
import { useToastStore } from '../../stores/toastStore';
import { CheckCircle, AlertCircle, AlertTriangle, Info, X } from 'lucide-react';

const icons = {
  success: <CheckCircle className="text-green-500" />,
  error: <AlertCircle className="text-red-500" />,
  warning: <AlertTriangle className="text-yellow-500" />,
  info: <Info className="text-blue-500" />,
};

const bgColors = {
  success: 'bg-green-100 border-green-400',
  error: 'bg-red-100 border-red-400',
  warning: 'bg-yellow-100 border-yellow-400',
  info: 'bg-blue-100 border-blue-400',
};

export const Toast: React.FC = () => {
  const { toasts, removeToast } = useToastStore();

  if (!toasts.length) return null;

  return (
    <div className="fixed top-4 right-4 z-50 flex flex-col gap-2">
      {toasts.map((toast) => (
        <div 
          key={toast.id}
          className={`flex items-center gap-3 p-3 rounded-md shadow-lg border ${bgColors[toast.type]}`}
        >
          {icons[toast.type]}
          <p className="text-sm text-gray-800">{toast.message}</p>
          <button onClick={() => removeToast(toast.id)}><X size={16} className="text-gray-500"/></button>
        </div>
      ))}
    </div>
  );
};
