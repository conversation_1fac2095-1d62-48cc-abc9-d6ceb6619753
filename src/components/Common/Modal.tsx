import React, { useEffect } from 'react';
import { X } from 'lucide-react';
import { useModalStore } from '../../stores/modalStore';

interface ModalProps {
  title: string;
  children: React.ReactNode;
}

const Modal: React.FC<ModalProps> = ({ title, children }) => {
  const closeModal = useModalStore((s) => s.closeModal);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') closeModal();
    };
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [closeModal]);

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex justify-center items-center z-50"
      onClick={closeModal}
    >
      <div 
        className="bg-adobe-bg rounded-lg shadow-xl w-full max-w-md m-4 flex flex-col"
        onClick={(e) => e.stopPropagation()} // Prevent closing when clicking inside
      >
        <header className="flex justify-between items-center p-4 border-b border-adobe">
          <h2 className="text-lg font-semibold">{title}</h2>
          <button onClick={closeModal} className="p-1 rounded-full hover:bg-adobe-secondary"><X size={20} /></button>
        </header>
        <div className="p-4 overflow-y-auto">
          {children}
        </div>
      </div>
    </div>
  );
};

export default Modal;
