import React, { useState, useEffect } from 'react';
import Modal from '../Common/Modal';
import { getProviderApi } from '../../services/providerFactory';
import { ProviderID } from '../../types';
import { Loader2, CheckCircle, XCircle, AlertTriangle, RefreshCw } from 'lucide-react';

const PROVIDER_IDS: ProviderID[] = ['openai', 'anthropic', 'google', 'groq', 'deepseek', 'openrouter', 'ollama'];

type StatusDetail = {
  status: 'loading' | 'success' | 'error' | 'unconfigured';
  latency?: number;
  modelsCount?: number;
  error?: string;
};

const StatusRow: React.FC<{ providerId: ProviderID }> = ({ providerId }) => {
    const [detail, setDetail] = useState<StatusDetail>({ status: 'loading' });

    const checkStatus = async () => {
        setDetail({ status: 'loading' });
        const start = Date.now();
        try {
            const api = getProviderApi(providerId);
            const models = await api.getModels();
            if (models.length > 0) {
                 setDetail({
                    status: 'success',
                    latency: Date.now() - start,
                    modelsCount: models.length
                });
            } else {
                 setDetail({
                    status: 'error',
                    error: 'Connected, but no models found.'
                });
            }
        } catch (error: any) {
            const isAuthError = /API key/i.test(error.message);
             setDetail({
                status: isAuthError ? 'unconfigured' : 'error',
                error: isAuthError ? 'API key not set or invalid.' : 'Connection failed.'
            });
        }
    };
    
    useEffect(() => {
        checkStatus();
    }, [providerId]);

    const ICONS = {
        loading: <Loader2 size={16} className="animate-spin text-yellow-500" />,
        success: <CheckCircle size={16} className="text-green-500" />,
        error: <XCircle size={16} className="text-red-500" />,
        unconfigured: <AlertTriangle size={16} className="text-orange-500" />,
    };

    return (
        <tr className="border-b border-adobe">
            <td className="p-2 font-semibold capitalize">{providerId}</td>
            <td className="p-2 text-center">{ICONS[detail.status]}</td>
            <td className="p-2 text-xs">{detail.latency ? `${detail.latency} ms` : 'N/A'}</td>
            <td className="p-2 text-xs">{detail.modelsCount ?? 'N/A'}</td>
            <td className="p-2 text-xs text-gray-400">{detail.error ?? 'OK'}</td>
            <td className="p-2 text-center">
                <button onClick={checkStatus} title="Re-check status">
                    <RefreshCw size={14} className="hover:text-blue-500" />
                </button>
            </td>
        </tr>
    );
}


const StatusModal: React.FC = () => {
  return (
    <Modal title="System Connection Status">
      <div className="max-h-[60vh] overflow-y-auto">
        <table className="w-full text-sm text-left">
            <thead className="bg-adobe-secondary sticky top-0">
                <tr className="border-b-2 border-adobe">
                    <th className="p-2">Provider</th>
                    <th className="p-2 text-center">Status</th>
                    <th className="p-2">Latency</th>
                    <th className="p-2">Models</th>
                    <th className="p-2">Details</th>
                    <th className="p-2 text-center">Check</th>
                </tr>
            </thead>
            <tbody>
                {PROVIDER_IDS.map(id => <StatusRow key={id} providerId={id} />)}
            </tbody>
        </table>
        <p className="text-xs text-gray-500 mt-4">
            This panel shows the real-time status of each AI provider. Latency is measured by fetching the model list.
        </p>
      </div>
    </Modal>
  );
};

export default StatusModal;
