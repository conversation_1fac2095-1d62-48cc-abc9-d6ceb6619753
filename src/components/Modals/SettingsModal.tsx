import React from 'react';
import Modal from '../Common/Modal';
import { useSettingsStore } from '../../stores/settingsStore';
import { useChatStore } from '../../stores/chatStore';
import { useToastStore } from '../../stores/toastStore';
import { Trash2 } from 'lucide-react';

const SettingsModal: React.FC = () => {
  const { theme, setTheme } = useSettingsStore();
  const { clearAllConversations } = useChatStore();
  const addToast = useToastStore(s => s.addToast);

  const handleClearHistory = () => {
    // In a real app, use a more robust confirmation dialog component
    if(window.confirm("Are you sure you want to delete all chat history? This action cannot be undone.")) {
        clearAllConversations();
        addToast({ message: "All chat history has been cleared.", type: "success" });
    }
  };

  return (
    <Modal title="Settings">
      <div className="flex flex-col gap-6">
        <div>
          <label htmlFor="theme-select" className="block text-sm font-medium mb-1">Theme</label>
          <select
            id="theme-select"
            value={theme}
            onChange={(e) => setTheme(e.target.value as 'light' | 'dark' | 'auto')}
            className="w-full p-2 rounded bg-adobe-secondary border border-adobe focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="auto">Auto (Sync with Adobe)</option>
            <option value="light">Light</option>
            <option value="dark">Dark</option>
          </select>
        </div>

        <div className="border-t border-adobe pt-4">
            <h4 className="text-md font-semibold mb-2">Data Management</h4>
             <button
                onClick={handleClearHistory}
                className="w-full flex items-center justify-center gap-2 px-3 py-2 rounded bg-red-600 hover:bg-red-700 text-white text-sm"
              >
                <Trash2 size={16} />
                Clear All Chat History
              </button>
              <p className="text-xs text-gray-500 mt-2">
                This will permanently delete all your conversations and messages stored within the extension.
              </p>
        </div>
      </div>
    </Modal>
  );
};

export default SettingsModal;
