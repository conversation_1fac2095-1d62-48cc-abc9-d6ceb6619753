import React from 'react';
import Modal from '../Common/Modal';
import { useChatStore } from '../../stores/chatStore';
import { useModalStore } from '../../stores/modalStore';

const HistoryModal: React.FC = () => {
  const { conversations, setCurrentConversationId } = useChatStore();
  const { closeModal } = useModalStore();

  const conversationList = Object.values(conversations);

  const handleSelectConversation = (conversationId: string) => {
    setCurrentConversationId(conversationId);
    closeModal();
  };

  return (
    <Modal title="Chat History">
      {conversationList.length === 0 ? (
        <p>No chat history yet.</p>
      ) : (
        <ul className="flex flex-col gap-2">
          {conversationList.map(convo => (
            <li
              key={convo.id}
              onClick={() => handleSelectConversation(convo.id)}
              className="p-2 rounded hover:bg-adobe-secondary cursor-pointer"
            >
              <p className="font-semibold">{convo.title}</p>
              <p className="text-xs opacity-70">{new Date(convo.createdAt).toLocaleString()}</p>
            </li>
          ))}
        </ul>
      )}
    </Modal>
  );
};

export default HistoryModal;
