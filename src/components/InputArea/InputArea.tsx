import React, { useState, useRef, KeyboardEvent, useEffect } from 'react';
import { Send, Paperclip, Mic, Loader2 } from 'lucide-react';
import { useChatStore } from '../../stores/chatStore';
import { useToastStore } from '../../stores/toastStore';

const InputArea: React.FC = () => {
  const [text, setText] = useState('');
  const [isListening, setIsListening] = useState(false);
  const { sendChatMessage, isLoading } = useChatStore();
  const addToast = useToastStore(s => s.addToast);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const recognitionRef = useRef<any>(null); // For SpeechRecognition instance

  const handleSend = () => {
    if (text.trim() && !isLoading) {
      sendChatMessage(text.trim());
      setText('');
      // Delay focus to allow UI to update
      setTimeout(() => textareaRef.current?.focus(), 0);
    }
  };
  
  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        handleSend();
    }
  };

  const handleVoiceInput = () => {
    if (isListening) {
        recognitionRef.current?.stop();
        setIsListening(false);
        return;
    }

    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    if (!SpeechRecognition) {
        addToast({ message: "Voice recognition is not supported in this browser.", type: 'error' });
        return;
    }

    recognitionRef.current = new SpeechRecognition();
    recognitionRef.current.continuous = true;
    recognitionRef.current.interimResults = true;
    recognitionRef.current.lang = 'en-US';

    recognitionRef.current.onstart = () => setIsListening(true);
    recognitionRef.current.onend = () => setIsListening(false);
    recognitionRef.current.onerror = (event: any) => {
        addToast({ message: `Voice recognition error: ${event.error}`, type: 'error' });
        setIsListening(false);
    };

    recognitionRef.current.onresult = (event: any) => {
      let interimTranscript = '';
      for (let i = event.resultIndex; i < event.results.length; ++i) {
        if (event.results[i].isFinal) {
          setText(prev => prev + event.results[i][0].transcript + ' ');
        } else {
          interimTranscript += event.results[i][0].transcript;
        }
      }
    };

    recognitionRef.current.start();
  };

  // Auto-resize textarea
  useEffect(() => {
    if(textareaRef.current) {
        textareaRef.current.style.height = 'auto';
        const scrollHeight = textareaRef.current.scrollHeight;
        textareaRef.current.style.height = `${scrollHeight}px`;
    }
  }, [text]);

  return (
    <div className="p-2 border-t border-adobe bg-adobe-bg flex-shrink-0">
      <div className="flex items-start gap-2 p-2 rounded-md bg-adobe-secondary">
        <button className="p-1.5 rounded" aria-label="Attach File"><Paperclip size={18} /></button>
        <button
          onClick={handleVoiceInput}
          className={`p-1.5 rounded ${isListening ? 'bg-red-500 text-white' : ''}`}
          aria-label="Voice Input"
        >
          <Mic size={18} />
        </button>
        <textarea
          ref={textareaRef}
          value={text}
          onChange={(e) => setText(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={isListening ? "Listening..." : "Type a message or use voice input..."}
          className="flex-grow bg-transparent focus:outline-none resize-none max-h-48 overflow-y-auto"
          rows={1}
          maxLength={4000}
          disabled={isLoading}
        />
        <button onClick={handleSend} disabled={isLoading || !text.trim()} className="p-1.5 rounded bg-blue-600 text-white disabled:bg-gray-500 self-end">
          {isLoading ? <Loader2 size={18} className="animate-spin" /> : <Send size={18} />}
        </button>
      </div>
    </div>
  );
};

export default InputArea;
