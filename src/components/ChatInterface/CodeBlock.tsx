import React, { useEffect, useState } from 'react';
import { highlightCode } from '../../utils/shiki';
import { Copy, Download, Terminal, ChevronDown, ChevronUp } from 'lucide-react';
import { evalScript } from '../../utils/cep';
import { useToastStore } from '../../stores/toastStore';

interface CodeBlockProps {
  code: string;
  language: string;
}

const CodeBlock: React.FC<CodeBlockProps> = ({ code, language }) => {
  const [highlightedHtml, setHighlightedHtml] = useState('');
  const [isCollapsed, setIsCollapsed] = useState(false);
  const addToast = useToastStore(s => s.addToast);

  useEffect(() => {
    highlightCode(code, language).then(setHighlightedHtml);
  }, [code, language]);
  
  const handleCopy = () => {
    navigator.clipboard.writeText(code);
    addToast({ message: 'Code copied to clipboard', type: 'success' });
  };
  
  const handleRun = async () => {
    try {
      const result = await evalScript(`runCode(${JSON.stringify(code)})`);
      addToast({ message: `Execution result: ${result.result || result.error}`, type: result.success ? 'info' : 'error' });
    } catch (e: any) {
      addToast({ message: `Execution failed: ${e.message}`, type: 'error' });
    }
  };

  return (
    <div className="bg-[#282c34] rounded-md my-2 text-sm overflow-hidden">
      <div className="flex justify-between items-center px-3 py-1 bg-gray-700 text-gray-300">
        <span className="font-mono">{language}</span>
        <div className="flex gap-1">
          <button onClick={() => setIsCollapsed(!isCollapsed)} className="p-1 hover:bg-gray-600 rounded">
            {isCollapsed ? <ChevronDown size={14} /> : <ChevronUp size={14} />}
          </button>
          <button onClick={handleCopy} className="p-1 hover:bg-gray-600 rounded"><Copy size={14} /></button>
          <button className="p-1 hover:bg-gray-600 rounded"><Download size={14} /></button>
          {['javascript', 'typescript', 'extendscript', 'jsx'].includes(language) && (
              <button onClick={handleRun} className="p-1 hover:bg-gray-600 rounded"><Terminal size={14} /></button>
          )}
        </div>
      </div>
      {!isCollapsed && (
        <div
          className="p-3 overflow-x-auto"
          dangerouslySetInnerHTML={{ __html: highlightedHtml }}
        />
      )}
    </div>
  );
};

export default CodeBlock;
