import React from 'react';
import { Message } from '../../types';
import CodeBlock from './CodeBlock';
import { <PERSON>r, <PERSON><PERSON>, RefreshCw } from 'lucide-react';
import { useChatStore } from '../../stores/chatStore';

const MessageBubble: React.FC<{ message: Message }> = ({ message }) => {
  const isUser = message.role === 'user';
  const { retryLastUserMessage } = useChatStore();

  const parts = message.content.split(/(```[\w\s]*\n[\s\S]*?\n```)/g);

  return (
    <div className={`flex items-start gap-3 ${isUser ? 'justify-end' : 'self-start'}`}>
      {!isUser && <div className="flex-shrink-0 w-8 h-8 rounded-full bg-adobe-secondary flex items-center justify-center"><Bot size={18} /></div>}
      <div className={`max-w-[85%] rounded-lg p-3 ${
          isUser
            ? 'bg-blue-600 text-white'
            : message.status === 'error' ? 'bg-red-100 text-red-800' : 'bg-adobe-secondary'
      }`}>
        {parts.map((part, index) => {
          if (part.startsWith('```')) {
            const match = part.match(/```(\w*)\n([\s\S]*?)\n```/);
            if (match) {
              const lang = match[1] || 'plaintext';
              const code = match[2] || '';
              return <CodeBlock key={index} code={code} language={lang} />;
            }
          }
          // Basic markdown for bolding error messages
          return <p key={index} className="whitespace-pre-wrap" dangerouslySetInnerHTML={{__html: part.replace(/\*\*(.*?)\*\*/g, '<b>$1</b>')}}></p>;
        })}

        {message.status === 'error' && !isUser && (
             <div className="mt-2 pt-2 border-t border-red-300 flex justify-end">
                 <button
                    onClick={retryLastUserMessage}
                    className="flex items-center gap-2 text-xs font-semibold text-red-700 hover:text-red-900"
                >
                    <RefreshCw size={12} />
                    Try again
                 </button>
            </div>
        )}
      </div>
      {isUser && <div className="flex-shrink-0 w-8 h-8 rounded-full bg-adobe-secondary flex items-center justify-center"><User size={18} /></div>}
    </div>
  );
};

export default MessageBubble;
