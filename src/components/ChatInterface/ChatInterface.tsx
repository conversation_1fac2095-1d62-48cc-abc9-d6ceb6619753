import React, { useRef, useEffect } from 'react';
import { useChatStore } from '../../stores/chatStore';
import MessageBubble from './MessageBubble';
import { Loader2 } from 'lucide-react';

const TypingIndicator: React.FC = () => (
    <div className="flex items-center gap-2 self-start">
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-adobe-secondary flex items-center justify-center">
            <Loader2 size={18} className="animate-spin" />
        </div>
        <div className="bg-adobe-secondary rounded-lg p-3">
            <div className="flex items-center gap-1.5">
                <span className="h-2 w-2 bg-gray-400 rounded-full animate-bounce delay-75"></span>
                <span className="h-2 w-2 bg-gray-400 rounded-full animate-bounce delay-150"></span>
                <span className="h-2 w-2 bg-gray-400 rounded-full animate-bounce delay-300"></span>
            </div>
        </div>
    </div>
);


const ChatInterface: React.FC = () => {
  const { conversations, currentConversationId, isLoading } = useChatStore();
  const messages = currentConversationId ? conversations[currentConversationId]?.messages : [];

  const chatContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Auto-scroll to bottom
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [messages]);

  return (
    <div ref={chatContainerRef} className="flex-grow p-4 overflow-y-auto">
      <div className="flex flex-col gap-4">
        {messages && messages.map((msg) => (
          <MessageBubble key={msg.id} message={msg} />
        ))}
        {isLoading && messages && messages.length > 0 && messages[messages.length-1].content === "" && (
            <TypingIndicator />
        )}
      </div>
    </div>
  );
};

export default ChatInterface;
