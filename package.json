{"name": "sahai-cep-extension", "version": "2.0.0", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"@adobe/cep-types": "^7.0.0", "clsx": "^2.1.1", "lucide-react": "^0.395.0", "react": "^18.2.0", "react-dom": "^18.2.0", "shiki": "^1.10.0", "tailwind-merge": "^2.3.0", "uuid": "^9.0.1", "zustand": "^4.5.2"}, "devDependencies": {"@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "typescript": "^5.2.2", "vite": "^5.2.0"}}