# SahAI CEP Extension

A modern AI Chat Bot Extension for Adobe Creative Suite with multi-provider support and native theme integration.

## Features

- **Multi-Provider Support**: OpenAI, Anthropic, Google, Groq, DeepSeek, OpenRouter, and Ollama
- **Native Adobe Theme Integration**: Automatically syncs with Adobe application themes
- **Code Execution**: Run ExtendScript code directly from chat responses
- **Syntax Highlighting**: Powered by <PERSON><PERSON> for beautiful code display
- **Modern UI**: Built with React, TypeScript, and Tailwind CSS
- **State Management**: Zustand for efficient state management
- **Circuit Breaker**: Resilient API calls with automatic retry logic

## Development Setup

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Development Mode**
   ```bash
   npm run dev
   ```

3. **Build for Production**
   ```bash
   npm run build
   ```

## Adobe CEP Installation

1. Build the extension using `npm run build`
2. Copy the entire project folder to your CEP extensions directory:
   - **Windows**: `C:\Users\<USER>\AppData\Roaming\Adobe\CEP\extensions\`
   - **macOS**: `~/Library/Application Support/Adobe/CEP/extensions/`

3. Enable debugging in Adobe applications:
   - Set the `PlayerDebugMode` flag to `1` in the registry (Windows) or plist (macOS)

4. Restart your Adobe application and find "SahAI Chat Bot" in the Window > Extensions menu

## Supported Adobe Applications

- After Effects (AEFT) - Version 18.0+
- Premiere Pro (PPRO) - Version 14.0+
- Photoshop (PHXS) - Version 22.0+
- Illustrator (ILST) - Version 24.0+
- Audition (AUDT) - Version 13.0+

## Configuration

1. Open the extension panel
2. Click the Settings icon to configure:
   - Theme preferences (Auto/Light/Dark)
   - API provider settings
3. Click the Provider icon to:
   - Add API keys for different providers
   - Select models for each provider

## Architecture

- **Frontend**: React 18 + TypeScript + Tailwind CSS
- **State Management**: Zustand stores
- **Build Tool**: Vite
- **Code Highlighting**: Shiki
- **Adobe Integration**: CEP (Common Extensibility Platform)
- **ExtendScript Bridge**: For Adobe application interaction

## Project Structure

```
SahAI_CEP_Extension/
├── CSXS/manifest.xml          # CEP manifest
├── public/                    # Static assets
├── src/
│   ├── components/           # React components
│   ├── stores/              # Zustand state stores
│   ├── services/            # API and business logic
│   ├── utils/               # Utility functions
│   ├── types/               # TypeScript definitions
│   └── styles/              # Global styles
└── dist/                    # Built extension (after npm run build)
```

## License

Copyright © 2024 SahAI. All rights reserved.
