### 1. `src/stores/settingsStore.ts` (Corrected)

**Fixes Applied:**
*   **(Fix for #1 & #5)** Added the `refreshProviderModels` function to fetch and cache models from a provider's API.
*   **(Fix for #5)** The `setProviderApiKey` function now automatically calls `refreshProviderModels` after a key is successfully saved, ensuring the model list is updated immediately.
*   **(Fix for #1)** Added the `setOllamaBaseUrl` function to properly save the Ollama configuration, which resolves the issue in `ProviderModal.tsx`.

```typescript
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { ProviderID, ProviderConfig, AdobeTheme, Model } from '../types';
import { getSecureCredential, setSecureCredential } from '../utils/cep';
import { getProviderApi } from '../services/providerFactory';
import { useToastStore } from './toastStore';

interface SettingsState {
  providers: Record<ProviderID, ProviderConfig>;
  selectedProvider: ProviderID;
  selectedModel: string;
  theme: 'light' | 'dark' | 'auto';
  adobeTheme: AdobeTheme | null;
  setProviderApiKey: (providerId: ProviderID, apiKey: string) => void;
  setOllamaBaseUrl: (baseUrl: string) => void;
  setSelectedProvider: (providerId: ProviderID) => void;
  setSelectedModel: (modelId: string) => void;
  setTheme: (theme: 'light' | 'dark' | 'auto', adobeTheme?: AdobeTheme) => void;
  applyTheme: () => void;
  refreshProviderModels: (providerId: ProviderID) => Promise<void>;
}

const initialProviders: Record<ProviderID, ProviderConfig> = {
  openai: { models: [] },
  anthropic: { models: [] },
  google: { models: [] },
  groq: { models: [] },
  deepseek: { models: [] },
  openrouter: { models: [] },
  ollama: { baseURL: 'http://localhost:11434', models: [] },
};

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set, get) => ({
      providers: initialProviders,
      selectedProvider: 'openai',
      selectedModel: '',
      theme: 'auto',
      adobeTheme: null,

      setProviderApiKey: (providerId, apiKey) => {
        setSecureCredential(providerId, apiKey);
        // Automatically refresh models after setting a new key.
        get().refreshProviderModels(providerId);
      },
      
      setOllamaBaseUrl: (baseUrl: string) => {
        set((state) => ({
          providers: {
            ...state.providers,
            ollama: { ...state.providers.ollama, baseURL: baseUrl },
          },
        }));
      },

      setSelectedProvider: (providerId) => {
        set({ selectedProvider: providerId, selectedModel: '' });
        // Refresh models when switching to a provider if the list is empty
        if (get().providers[providerId].models.length === 0) {
            get().refreshProviderModels(providerId);
        }
      },
      
      setSelectedModel: (modelId) => set({ selectedModel: modelId }),
      
      setTheme: (theme, adobeTheme) => set({ theme, ...(adobeTheme && { adobeTheme }) }),
      
      applyTheme: () => {
        const { theme, adobeTheme } = get();
        const root = document.documentElement;
        if ((theme === 'auto' && adobeTheme) || theme !== 'auto') {
            const isDark = theme === 'dark' || (adobeTheme && parseInt(adobeTheme.backgroundColor.substring(1), 16) < 0x808080);
            if (isDark) {
                root.style.setProperty('--adobe-bg-color', '#323232');
                root.style.setProperty('--adobe-text-color', '#F0F0F0');
                root.style.setProperty('--adobe-secondary-bg-color', '#3C3C3C');
                root.style.setProperty('--adobe-border-color', '#4A4A4A');
                root.style.setProperty('--adobe-scrollbar-thumb-color', '#555555');
                root.style.setProperty('--adobe-scrollbar-track-color', '#323232');
            } else {
                root.style.setProperty('--adobe-bg-color', '#F5F5F5');
                root.style.setProperty('--adobe-text-color', '#1a1a1a');
                root.style.setProperty('--adobe-secondary-bg-color', '#EAEAEA');
                root.style.setProperty('--adobe-border-color', '#D3D3D3');
                root.style.setProperty('--adobe-scrollbar-thumb-color', '#C1C1C1');
                root.style.setProperty('--adobe-scrollbar-track-color', '#F5F5F5');
            }
        }
      },

      refreshProviderModels: async (providerId) => {
        try {
          const api = getProviderApi(providerId);
          const models: Model[] = await api.getModels();
          set((state) => ({
            providers: {
              ...state.providers,
              [providerId]: { ...state.providers[providerId], models },
            },
          }));
          // Auto-select the first model if none is selected
          if (!get().selectedModel && models.length > 0) {
            get().setSelectedModel(models[0].id);
          }
        } catch (error: any) {
          console.error(`Failed to fetch models for ${providerId}:`, error);
          useToastStore.getState().addToast({
            message: `Could not fetch models for ${providerId}. Check API key and connection.`,
            type: 'error',
          });
          // Clear stale models on error
           set((state) => ({
            providers: {
              ...state.providers,
              [providerId]: { ...state.providers[providerId], models: [] },
            },
          }));
        }
      },
    }),
    {
      name: 'sahai-settings-storage',
      partialize: (state) => ({
        selectedProvider: state.selectedProvider,
        selectedModel: state.selectedModel,
        theme: state.theme,
        providers: { ollama: state.providers.ollama }, // Persist Ollama URL
      }),
    }
  )
);
```

### 2. `src/stores/chatStore.ts` (Corrected)

**Fixes Applied:**
*   **(Support for #2)** Refactored to support streaming responses.
*   Added `sendChatMessage` to orchestrate the entire API call flow.
*   Added `appendTokenToLastMessage` to append incoming text chunks to the assistant's message, creating the "typing" effect.
*   Added `clearAllConversations` for use in the Settings modal.

```typescript
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { v4 as uuidv4 } from 'uuid';
import { Conversation, Message, ProviderID } from '../types';
import { getProviderApi } from '../services/providerFactory';
import { useToastStore } from './toastStore';

interface ChatState {
  conversations: Record<string, Conversation>;
  currentConversationId: string | null;
  isLoading: boolean;
  addMessage: (message: Omit<Message, 'id' | 'timestamp'>, conversationId: string) => void;
  appendTokenToLastMessage: (token: string) => void;
  sendChatMessage: (content: string) => Promise<void>;
  startNewConversation: () => void;
  setCurrentConversationId: (id: string) => void;
  clearAllConversations: () => void;
}

const createNewConversation = (): Conversation => ({
  id: uuidv4(),
  title: 'New Chat',
  messages: [],
  createdAt: new Date().toISOString(),
});

export const useChatStore = create<ChatState>()(
  persist(
    (set, get) => ({
      conversations: {},
      currentConversationId: null,
      isLoading: false,

      addMessage: (messageContent, conversationId) => {
        set((state) => {
          const conversation = state.conversations[conversationId];
          if (!conversation) return {};

          const newMessage: Message = {
            ...messageContent,
            id: uuidv4(),
            timestamp: new Date().toISOString(),
          };

          return {
            conversations: {
              ...state.conversations,
              [conversationId]: {
                ...conversation,
                messages: [...conversation.messages, newMessage],
              },
            },
          };
        });
      },
      
      appendTokenToLastMessage: (token) => {
          set(state => {
              const { currentConversationId, conversations } = state;
              if (!currentConversationId || !conversations[currentConversationId]) return {};
              
              const currentMessages = conversations[currentConversationId].messages;
              if (currentMessages.length === 0) return {};
              
              const lastMessage = currentMessages[currentMessages.length - 1];
              if (lastMessage.role !== 'assistant') return {};
              
              const updatedLastMessage = { ...lastMessage, content: lastMessage.content + token };
              
              return {
                  conversations: {
                      ...state.conversations,
                      [currentConversationId]: {
                          ...conversations[currentConversationId],
                          messages: [...currentMessages.slice(0, -1), updatedLastMessage]
                      }
                  }
              }
          })
      },

      sendChatMessage: async (content: string) => {
        let { currentConversationId } = get();
        
        // If there's no active conversation, create one
        if (!currentConversationId) {
            const newConvo = createNewConversation();
            set(state => ({ 
                conversations: { ...state.conversations, [newConvo.id]: newConvo },
                currentConversationId: newConvo.id
            }));
            currentConversationId = newConvo.id;
        }

        // Add user message
        get().addMessage({ role: 'user', content }, currentConversationId!);
        
        // Add empty assistant message placeholder
        get().addMessage({ role: 'assistant', content: '' }, currentConversationId!);

        set({ isLoading: true });
        
        try {
          const { useSettingsStore } = await import('./settingsStore');
          const { selectedProvider, selectedModel } = useSettingsStore.getState();
          const api = getProviderApi(selectedProvider as ProviderID);
          const messages = get().conversations[currentConversationId!].messages.slice(0, -1); // Exclude the placeholder

          const stream = api.chat(messages, selectedModel);
          for await (const chunk of stream) {
            get().appendTokenToLastMessage(chunk);
          }
        } catch (error: any) {
            get().appendTokenToLastMessage(`\n\n**Error:** ${error.message}`);
            useToastStore.getState().addToast({ message: error.message, type: 'error' });
        } finally {
            set({ isLoading: false });
        }
      },

      startNewConversation: () => {
        const newConvo = createNewConversation();
        set(state => ({
            conversations: { ...state.conversations, [newConvo.id]: newConvo },
            currentConversationId: newConvo.id
        }));
      },
      
      setCurrentConversationId: (id) => set({ currentConversationId: id }),
      
      clearAllConversations: () => set({ conversations: {}, currentConversationId: null }),

    }),
    {
      name: 'sahai-chat-storage',
    }
  )
);
```

### 3. `src/components/InputArea/InputArea.tsx` (Corrected)

**Fixes Applied:**
*   **(Fix for #2)** The `handleSend` function now calls `chatStore.sendChatMessage`, triggering the complete, streaming API call flow. The placeholder `// TODO` has been removed.

```typescript
import React, { useState, useRef, KeyboardEvent, useEffect } from 'react';
import { Send, Paperclip, Mic, Loader2 } from 'lucide-react';
import { useChatStore } from '../../stores/chatStore';

const InputArea: React.FC = () => {
  const [text, setText] = useState('');
  const { sendChatMessage, isLoading } = useChatStore();
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSend = () => {
    if (text.trim() && !isLoading) {
      sendChatMessage(text.trim());
      setText('');
      // Delay focus to allow UI to update
      setTimeout(() => textareaRef.current?.focus(), 0);
    }
  };
  
  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        handleSend();
    }
  };

  // Auto-resize textarea
  useEffect(() => {
    if(textareaRef.current) {
        textareaRef.current.style.height = 'auto';
        const scrollHeight = textareaRef.current.scrollHeight;
        textareaRef.current.style.height = `${scrollHeight}px`;
    }
  }, [text]);

  return (
    <div className="p-2 border-t border-adobe bg-adobe-bg flex-shrink-0">
      <div className="flex items-start gap-2 p-2 rounded-md bg-adobe-secondary">
        <button className="p-1.5 rounded" aria-label="Attach File"><Paperclip size={18} /></button>
        <textarea
          ref={textareaRef}
          value={text}
          onChange={(e) => setText(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Type a message or drop a file..."
          className="flex-grow bg-transparent focus:outline-none resize-none max-h-48 overflow-y-auto"
          rows={1}
          maxLength={4000}
          disabled={isLoading}
        />
        <button onClick={handleSend} disabled={isLoading || !text.trim()} className="p-1.5 rounded bg-blue-600 text-white disabled:bg-gray-500 self-end">
          {isLoading ? <Loader2 size={18} className="animate-spin" /> : <Send size={18} />}
        </button>
      </div>
      <div className="text-xs text-right mt-1 opacity-70 pr-2">
        {text.length} / 4000
      </div>
    </div>
  );
};

export default InputArea;
```

### 4. `src/components/ChatInterface/ChatInterface.tsx` (Corrected)

**Fixes Applied:**
*   **(Fix for #3)** Added a "typing indicator" component that renders when `isLoading` is true and the last message is an empty assistant message. This provides clear visual feedback during stream generation.

```typescript
import React, { useRef, useEffect } from 'react';
import { useChatStore } from '../../stores/chatStore';
import MessageBubble from './MessageBubble';
import { Loader2 } from 'lucide-react';

const TypingIndicator: React.FC = () => (
    <div className="flex items-center gap-2 self-start">
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-adobe-secondary flex items-center justify-center">
            <Loader2 size={18} className="animate-spin" />
        </div>
        <div className="bg-adobe-secondary rounded-lg p-3">
            <div className="flex items-center gap-1.5">
                <span className="h-2 w-2 bg-gray-400 rounded-full animate-bounce delay-75"></span>
                <span className="h-2 w-2 bg-gray-400 rounded-full animate-bounce delay-150"></span>
                <span className="h-2 w-2 bg-gray-400 rounded-full animate-bounce delay-300"></span>
            </div>
        </div>
    </div>
);


const ChatInterface: React.FC = () => {
  const { conversations, currentConversationId, isLoading } = useChatStore();
  const messages = currentConversationId ? conversations[currentConversationId]?.messages : [];
  
  const chatContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Auto-scroll to bottom
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [messages]);

  return (
    <div ref={chatContainerRef} className="flex-grow p-4 overflow-y-auto">
      <div className="flex flex-col gap-4">
        {messages && messages.map((msg) => (
          <MessageBubble key={msg.id} message={msg} />
        ))}
        {isLoading && messages && messages.length > 0 && messages[messages.length-1].content === "" && (
            <TypingIndicator />
        )}
      </div>
    </div>
  );
};

export default ChatInterface;

```

### 5. `src/components/TopBar/StatusIndicator.tsx` (Corrected)

**Fixes Applied:**
*   **(Fix for #4)** Replaced the mock health check with a real implementation. It now calls `getProviderApi().getModels()` for the currently selected provider to verify the connection and measure latency. It runs on an interval and also updates when the selected provider changes.

```typescript
import React, { useState, useEffect, useCallback } from 'react';
import { Loader2 } from 'lucide-react';
import { useSettingsStore } from '../../stores/settingsStore';
import { getProviderApi } from '../../services/providerFactory';
import { ProviderID } from '../../types';

type Status = 'loading' | 'ok' | 'error' | 'idle';

const StatusIndicator: React.FC = () => {
    const selectedProvider = useSettingsStore(s => s.selectedProvider);
    const [status, setStatus] = useState<Status>('idle');
    const [latency, setLatency] = useState<number | null>(null);

    const checkConnection = useCallback(async () => {
      if (!selectedProvider) {
          setStatus('idle');
          return;
      }
      setStatus('loading');
      const start = Date.now();
      try {
        const api = getProviderApi(selectedProvider as ProviderID);
        // Using getModels as a health check is a common pattern
        // when a dedicated /health endpoint is not available.
        await api.getModels();
        setLatency(Date.now() - start);
        setStatus('ok');
      } catch (e) {
        console.warn(`Health check failed for ${selectedProvider}:`, e);
        setStatus('error');
        setLatency(null);
      }
    }, [selectedProvider]);

    useEffect(() => {
        checkConnection(); // Initial check
        const interval = setInterval(checkConnection, 30000); // Check every 30 seconds
        return () => clearInterval(interval);
    }, [checkConnection]);

    const colorMap: Record<Status, string> = {
        idle: 'bg-gray-400',
        loading: 'bg-yellow-500 animate-pulse',
        ok: 'bg-green-500',
        error: 'bg-red-500',
    };
    
    const titleMap: Record<Status, string> = {
        idle: 'Status: Idle',
        loading: 'Status: Checking connection...',
        ok: `Status: Connected | Latency: ${latency ?? 'N/A'}ms`,
        error: 'Status: Connection failed',
    };

    return (
        <div className="flex items-center gap-2" title={titleMap[status]}>
            {status === 'loading' ? 
              <Loader2 size={14} className="animate-spin text-yellow-500" /> : 
              <div className={`w-3 h-3 rounded-full ${colorMap[status]}`} />
            }
        </div>
    );
};

export default StatusIndicator;
```

### 6. `src/components/Modals/SettingsModal.tsx` (Corrected)

**Fixes Applied:**
*   **(Fix for #6)** The placeholder comment has been replaced with a functional "Clear All Chat History" button, providing a useful utility in the settings panel. A confirmation dialog is included to prevent accidental data loss.

```typescript
import React from 'react';
import Modal from '../Common/Modal';
import { useSettingsStore } from '../../stores/settingsStore';
import { useChatStore } from '../../stores/chatStore';
import { useToastStore } from '../../stores/toastStore';
import { Trash2 } from 'lucide-react';

const SettingsModal: React.FC = () => {
  const { theme, setTheme } = useSettingsStore();
  const { clearAllConversations } = useChatStore();
  const addToast = useToastStore(s => s.addToast);

  const handleClearHistory = () => {
    // In a real app, use a more robust confirmation dialog component
    if(window.confirm("Are you sure you want to delete all chat history? This action cannot be undone.")) {
        clearAllConversations();
        addToast({ message: "All chat history has been cleared.", type: "success" });
    }
  };

  return (
    <Modal title="Settings">
      <div className="flex flex-col gap-6">
        <div>
          <label htmlFor="theme-select" className="block text-sm font-medium mb-1">Theme</label>
          <select 
            id="theme-select"
            value={theme}
            onChange={(e) => setTheme(e.target.value as 'light' | 'dark' | 'auto')}
            className="w-full p-2 rounded bg-adobe-secondary border border-adobe focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="auto">Auto (Sync with Adobe)</option>
            <option value="light">Light</option>
            <option value="dark">Dark</option>
          </select>
        </div>
        
        <div className="border-t border-adobe pt-4">
            <h4 className="text-md font-semibold mb-2">Data Management</h4>
             <button
                onClick={handleClearHistory}
                className="w-full flex items-center justify-center gap-2 px-3 py-2 rounded bg-red-600 hover:bg-red-700 text-white text-sm"
              >
                <Trash2 size={16} />
                Clear All Chat History
              </button>
              <p className="text-xs text-gray-500 mt-2">
                This will permanently delete all your conversations and messages stored within the extension.
              </p>
        </div>
      </div>
    </Modal>
  );
};

export default SettingsModal;
```

### 7. `src/extendscript/main.jsx` (Corrected)

**Fixes Applied:**
*   **(Fix for #7)** The placeholder polyfill has been replaced with the robust and industry-standard `JSON2.js` polyfill by Douglas Crockford. This ensures compatibility with older Adobe host applications that may lack a native `JSON` object.

```javascript
/*
  SahAI ExtendScript Bridge
  This file is executed in the host application's engine.
  It provides functions to interact with the Adobe application context.
*/

// Full JSON2 Polyfill by Douglas Crockford (public domain).
// This ensures JSON functionality in older Adobe hosts.
if (typeof JSON === 'undefined') {
    JSON = {};
    (function () {
        'use strict';
        function f(n) {
            return n < 10 ? '0' + n : n;
        }
        if (typeof Date.prototype.toJSON !== 'function') {
            Date.prototype.toJSON = function () {
                return isFinite(this.valueOf())
                    ? this.getUTCFullYear() + '-' +
                        f(this.getUTCMonth() + 1) + '-' +
                        f(this.getUTCDate()) + 'T' +
                        f(this.getUTCHours()) + ':' +
                        f(this.getUTCMinutes()) + ':' +
                        f(this.getUTCSeconds()) + 'Z'
                    : null;
            };
            String.prototype.toJSON = Number.prototype.toJSON = Boolean.prototype.toJSON = function () {
                return this.valueOf();
            };
        }
        var cx, escapable, gap, indent, meta, rep;
        function quote(string) {
            escapable.lastIndex = 0;
            return escapable.test(string) ? '"' + string.replace(escapable, function (a) {
                var c = meta[a];
                return typeof c === 'string'
                    ? c
                    : '\\u' + ('0000' + a.charCodeAt(0).toString(16)).slice(-4);
            }) + '"' : '"' + string + '"';
        }
        function str(key, holder) {
            var i, k, v, length, mind = gap, partial, value = holder[key];
            if (value && typeof value === 'object' &&
                    typeof value.toJSON === 'function') {
                value = value.toJSON(key);
            }
            if (typeof rep === 'function') {
                value = rep.call(holder, key, value);
            }
            switch (typeof value) {
            case 'string':
                return quote(value);
            case 'number':
                return isFinite(value) ? String(value) : 'null';
            case 'boolean':
            case 'null':
                return String(value);
            case 'object':
                if (!value) {
                    return 'null';
                }
                gap += indent;
                partial = [];
                if (Object.prototype.toString.apply(value) === '[object Array]') {
                    length = value.length;
                    for (i = 0; i < length; i += 1) {
                        partial[i] = str(i, value) || 'null';
                    }
                    v = partial.length === 0
                        ? '[]'
                        : gap
                        ? '[\n' + gap + partial.join(',\n' + gap) + '\n' + mind + ']'
                        : '[' + partial.join(',') + ']';
                    gap = mind;
                    return v;
                }
                if (rep && typeof rep === 'object') {
                    length = rep.length;
                    for (i = 0; i < length; i += 1) {
                        if (typeof rep[i] === 'string') {
                            k = rep[i];
                            v = str(k, value);
                            if (v) {
                                partial.push(quote(k) + (gap ? ': ' : ':') + v);
                            }
                        }
                    }
                } else {
                    for (k in value) {
                        if (Object.prototype.hasOwnProperty.call(value, k)) {
                            v = str(k, value);
                            if (v) {
                                partial.push(quote(k) + (gap ? ': ' : ':') + v);
                            }
                        }
                    }
                }
                v = partial.length === 0
                    ? '{}'
                    : gap
                    ? '{\n' + gap + partial.join(',\n' + gap) + '\n' + mind + '}'
                    : '{' + partial.join(',') + '}';
                gap = mind;
                return v;
            }
        }
        escapable = /[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g;
        meta = {
            '\b': '\\b', '\t': '\\t', '\n': '\\n', '\f': '\\f', '\r': '\\r', '"': '\\"', '\\': '\\\\'
        };
        if (typeof JSON.stringify !== 'function') {
            JSON.stringify = function (value, replacer, space) {
                var i;
                gap = '';
                indent = '';
                if (typeof space === 'number') {
                    for (i = 0; i < space; i += 1) {
                        indent += ' ';
                    }
                } else if (typeof space === 'string') {
                    indent = space;
                }
                rep = replacer;
                if (replacer && typeof replacer !== 'function' &&
                        (typeof replacer !== 'object' ||
                        typeof replacer.length !== 'number')) {
                    throw new Error('JSON.stringify');
                }
                return str('', {'': value});
            };
        }
        var rx_one = /^[\],:{}\s]*$/,
            rx_two = /\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,
            rx_three = /"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,
            rx_four = /(?:^|:|,)(?:\s*\[)+/g;
        if (typeof JSON.parse !== 'function') {
            JSON.parse = function (text, reviver) {
                var j;
                function walk(holder, key) {
                    var k, v, value = holder[key];
                    if (value && typeof value === 'object') {
                        for (k in value) {
                            if (Object.prototype.hasOwnProperty.call(value, k)) {
                                v = walk(value, k);
                                if (v !== undefined) {
                                    value[k] = v;
                                } else {
                                    delete value[k];
                                }
                            }
                        }
                    }
                    return reviver.call(holder, key, value);
                }
                text = String(text);
                if (rx_one.test(text.replace(rx_two, '@')
                        .replace(rx_three, ']')
                        .replace(rx_four, ''))) {
                    j = eval('(' + text + ')');
                    return typeof reviver === 'function'
                        ? walk({'': j}, '')
                        : j;
                }
                throw new SyntaxError('JSON.parse');
            };
        }
    }());
}

/**
 * Get information about the host application and the active document.
 * @returns {String} A JSON string containing host and document info.
 */
function getHostInfo() {
    try {
        var info = {
            appName: app.name,
            appVersion: app.version,
            os: $.os,
            docName: (app.documents.length > 0 && app.activeDocument) ? app.activeDocument.name : 'No active document',
            docPath: (app.documents.length > 0 && app.activeDocument) ? app.activeDocument.fullName.fsName : 'N/A'
        };
        return JSON.stringify(info);
    } catch (e) {
        return JSON.stringify({ error: e.toString() });
    }
}

/**
 * Executes a given script snippet in the host's ExtendScript engine.
 * @param {String} script The code to execute.
 * @returns {String} The result of the execution, or an error message, as a JSON string.
 */
function runCode(script) {
    try {
        var result = (function() {
            return eval(script);
        })();

        var resultString;
        if (result === undefined) {
            resultString = 'undefined';
        } else if (result === null) {
            resultString = 'null';
        } else {
            try {
                resultString = result.toString();
                 if (resultString === '[object Object]') {
                    resultString = JSON.stringify(result);
                }
            } catch (e) {
                resultString = "Result could not be serialized to a string.";
            }
        }
        
        return JSON.stringify({ success: true, result: resultString });
    } catch (e) {
        return JSON.stringify({ success: false, error: e.toString(), line: e.line, file: e.fileName });
    }
}
```