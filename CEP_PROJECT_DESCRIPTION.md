# Sah<PERSON>I CEP Extension - Complete Project Description

## Overview
SahAI is a comprehensive AI Chat Bot Extension designed for Adobe Creative Suite applications. The extension provides seamless integration between Adobe's creative tools and multiple AI providers, enabling users to interact with various AI models directly within their creative workflow. The extension operates as a CEP (Common Extensibility Platform) panel that can be docked or floated within Adobe applications like After Effects, Photoshop, Illustrator, and other Creative Suite programs.

## Core Purpose
The extension serves as a unified interface for accessing multiple AI providers and their language models, allowing creative professionals to leverage AI assistance without leaving their Adobe workspace. Users can engage in conversational AI interactions, receive creative guidance, and potentially execute AI-generated code or scripts within their Adobe applications.

## Architecture Components

### User Interface Layer
The extension features a modern, Adobe-themed interface built with React and TypeScript, consisting of several key components:

**Top Navigation Bar**: Displays the current AI provider status, selected model information, and provides quick access to core functions. Contains buttons for creating new chat sessions, accessing chat history, and opening settings. The provider selector shows real-time loading states and connection status.

**Chat Interface**: Features a scrollable message area with message bubbles that support advanced syntax highlighting through Shiki integration. Messages are displayed in a conversational format with clear distinction between user inputs and AI responses. The interface includes an auto-scroll mechanism and a scroll-to-bottom button for navigation.

**Code Block System**: Integrates Shiki syntax highlighter with support for twenty comprehensive programming languages covering web development, mobile development, systems programming, and scripting: JavaScript, TypeScript, TypeScript React, HTML, CSS, JSON, XML, Markdown, YAML, SCSS, Less, Python, Swift, Rust, Go, Java, PHP, Ruby, and Shell. Each code block features a comprehensive toolbar with multiple interactive functions:
- **Copy Function**: One-click copying of code to clipboard using Lucide Copy icon
- **Save Function**: Download code as files using Lucide Download icon
- **Collapse/Expand Function**: Toggle code visibility with Lucide ChevronDown and ChevronUp icons
- **Run Function**: Execute JavaScript, TypeScript, and TypeScript React code directly in ExtendScript context using Lucide Terminal icon
- **Language Detection**: Automatic language identification with fallback mapping for unsupported languages
- **Theme Integration**: Uses GitHub Dark theme for consistent Adobe interface integration

**Input Area**: Provides a comprehensive text input interface with multiple interaction methods:
- **Attach Function**: File attachment capability using Lucide Paperclip icon
- **Voice Input**: Speech-to-text functionality using Lucide Mic icon. Use 'https://developer.mozilla.org/en-US/docs/Web/API/SpeechRecognition' or similar best open-source option
- **Send Function**: Message submission using Lucide Send icon with loading state using Lucide Loader2 icon
- **Auto-resize Textarea**: Dynamic height adjustment based on content length
- **Character Limit**: 4000 character maximum with visual feedback when approaching limit
- **Keyboard Shortcuts**: Enter to send, Shift+Enter for new lines
- **Composition Support**: Proper handling of international input methods

**Modal System**: Implements a comprehensive modal overlay system for provider configuration, settings management, chat history browsing, and system status monitoring. Each modal uses Lucide icons for consistent visual language throughout the interface.

**Icon System**: Utilizes Lucide React icon library throughout the interface for consistent, scalable vector graphics:
- **Navigation Icons**: Plus (new chat), History (chat history), Settings (configuration), ChevronDown (dropdowns)
- **Status Icons**: Loader2 (loading states), CheckCircle (success), AlertCircle (errors), AlertTriangle (warnings), Info (information)
- **Interactive Icons**: X (close), Wifi/WifiOff (connection status), Copy (clipboard), Download (save), Terminal (execute)
- **Input Icons**: Paperclip (attach), Mic (voice), Send (submit), ChevronUp/ChevronDown (expand/collapse)

**Toast Notification System**: Provides real-time user feedback through a sophisticated notification system:
- **Four Notification Types**: Success (green), Error (red), Warning (yellow), Info (blue)
- **Auto-dismiss Functionality**: Configurable duration with smooth slide-out animations
- **Manual Dismissal**: Close button with X icon for immediate removal
- **Visual Hierarchy**: Color-coded left borders and matching icons for instant recognition
- **Positioning**: Fixed top-right placement with z-index management for proper layering

### AI Provider Integration
The extension supports seven major AI providers, each with specific configuration requirements and capabilities:

**OpenAI**: The default provider offering access to GPT models.

**Anthropic**: Supports Claude models.

**Google Gemini**: Integrates with Google's Gemini Pro and Gemini Flash models.

**Groq**: Provides access to fast inference models. Uses OpenAI-compatible API endpoints with API key authentication.

**DeepSeek**: Offers specialized models including DeepSeek Chat for general purposes and DeepSeek Coder for programming tasks.

**OpenRouter**: Acts as a gateway to multiple AI providers including OpenAI, Anthropic, and Meta models through a unified API. 

**Ollama**: Enables local AI model execution through Ollama's local server. Uses base URL configuration (typically localhost:11434) instead of API keys. Supports model pulling and management for local inference.

### Provider Configuration System
Each provider has distinct configuration requirements:

**API Key Providers**: OpenAI, Anthropic, Gemini, Groq, DeepSeek, and OpenRouter require secure API key storage and validation.

**Base URL Provider**: Ollama uses local server URL configuration with connection validation and model discovery.

Each selected Provider should load their latest models dynamically allowing users to explore options before API key authentication..

### Model Management
The extension maintains comprehensive model catalogs for each provider, including:

**Model Metadata**: Each model includes identification, display names, descriptions, context length specifications, and recommendation status.

**Dynamic Loading**: Models are fetched in real-time from provider.

**Selection Interface**: Searchable dropdown interfaces allow users to browse and select from available models with filtering and sorting capabilities.

### Adobe Integration Layer
The extension communicates with Adobe applications through ExtendScript integration:

**Host Script Communication**: A comprehensive ExtendScript bridge handles communication between the CEP panel and Adobe applications, providing access to application information, document details, and execution capabilities.

**Circuit Breaker Pattern**: Implements robust error handling and retry mechanisms to prevent cascading failures in Adobe communication.

**Theme Integration**: Automatically adapts to Adobe's native theme system, providing consistent visual integration with the host application.

### State Management
The extension employs sophisticated state management through multiple specialized stores:

**Settings Store**: Manages provider configurations, API keys, selected models, and user preferences with persistent storage.

**Chat Store**: Handles message history, conversation sessions, and loading states with session management capabilities.

**Modal Store**: Controls the display and state of various modal interfaces throughout the application.

**Toast Store**: Manages user notifications and feedback messages with configurable display durations.

### Error Handling and Reliability
The system includes comprehensive error handling mechanisms:

**Error Boundary System**: Multi-layered error containment using React Error Boundaries:
- **Global Error Boundaries**: Wrap each major component (TopBar, ChatMessages, InputArea, ModalRoot, ToastContainer) to prevent cascading failures
- **Provider Error Boundaries**: Specialized error handling for provider-specific failures with automatic retry logic and error count tracking
- **Graceful Degradation**: Custom fallback UI with AlertTriangle icons and user-friendly error messages
- **Recovery Mechanisms**: "Try again" functionality to reset error states without full application reload

**Connection Validation**: Real-time provider status monitoring:
- **Status Indicator**: Visual connection status using colored dots (green for online, red for offline, yellow for checking)
- **Latency Monitoring**: Response time tracking for performance awareness
- **Periodic Health Checks**: 30-second interval status polling for active providers
- **Connection Diagnostics**: Detailed status modal with connection information and troubleshooting

**Retry Logic**: Implements intelligent retry mechanisms for failed requests with exponential backoff.

**Circuit Breaker**: Prevents repeated failures from overwhelming the system by temporarily disabling problematic operations with failure count tracking and recovery timeouts.

### Security and Privacy
The extension implements security best practices:

**Secure Credential Storage**: API keys and sensitive configuration data are stored securely within the CEP environment.

**Local Processing**: Supports local AI inference through Ollama for privacy-conscious users.

**Connection Validation**: Verifies provider connections and credentials before exposing functionality.

## User Experience Flow
Users interact with the extension through a streamlined workflow: they select an AI provider and model through the provider modal, configure necessary credentials, engage in conversational interactions through the chat interface, and access additional features through the modal system. The interface provides real-time feedback on connection status, model loading, and operation progress.

## Technical Foundation
Built on modern web technologies including React, TypeScript, and Zustand for state management, the extension leverages Adobe's CEP framework for deep integration with Creative Suite applications. Key technical components include:

**Frontend Technologies**:
- **React 18**: Modern component-based UI framework with hooks and functional components
- **TypeScript**: Type-safe development with comprehensive interface definitions
- **Zustand**: Lightweight state management with subscription-based updates
- **Tailwind CSS**: Utility-first styling with Adobe theme integration
- **Shiki**: Advanced syntax highlighting with WebAssembly-based grammar parsing supporting 20 programming languages including web technologies (JavaScript, TypeScript, TypeScript React, HTML, CSS, SCSS, Less), data formats (JSON, XML, YAML, Markdown), and popular programming languages (Python, Swift, Rust, Go, Java, PHP, Ruby, Shell)
- **Lucide React**: Comprehensive icon library with 1000+ scalable vector icons

**Adobe Integration**:
- **CEP Framework**: Common Extensibility Platform for Creative Suite integration
- **ExtendScript Bridge**: JavaScript execution within Adobe applications
- **Theme Synchronization**: Automatic adaptation to Adobe's native color schemes
- **Host Application Communication**: Bidirectional data exchange with Creative Suite apps

**Architecture Patterns**:
- **Component Composition**: Modular UI components with clear separation of concerns
- **Error Boundaries**: Fault isolation and graceful error handling
- **Circuit Breaker Pattern**: Resilient external service communication
- **Observer Pattern**: Reactive state updates with subscription management

The extension represents a comprehensive solution for integrating AI capabilities into creative workflows, providing professional users with powerful AI assistance while maintaining the familiar Adobe interface paradigms and ensuring reliable operation within the Creative Suite ecosystem. The combination of modern web technologies, robust error handling, and deep Adobe integration creates a seamless bridge between AI capabilities and creative professional workflows.
